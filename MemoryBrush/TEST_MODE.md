# 🧪 MemoryBrush 测试模式使用指南

## 📋 功能说明

测试模式是为了方便开发和测试而添加的功能，可以让您无需逐关通过就能访问所有关卡。

## 🚀 如何启用测试模式

### 通过配置文件
编辑 `frontend/src/config/testMode.ts` 文件：

```typescript
export const TEST_MODE_CONFIG = {
  // 设置为 true 启用测试模式，false 关闭测试模式
  enabled: true,
  // ... 其他配置
}
```

保存文件后刷新页面即可生效。

## 🎮 测试模式功能

### ✅ 已解锁的内容
- **所有级别**: level-1, level-2, level-3, level-4
- **所有关卡** (统一使用 Lx-y 格式):
  - L1-1: 自由画线
  - L1-2: 匀速直线
  - L1-3: 匀速线条组合
  - L1-4: 曲线消除
  - L1-5: 直线图形
  - L1-6: 曲线图形
  - L2-1, L2-2, L2-3, L2-4: 立体空间相关关卡
  - L3-1, L3-2, L3-3, L3-4: 画面构图相关关卡
  - L4-1, L4-2, L4-3, L4-4: 智能创作相关关卡

### 🔄 状态管理
- 测试模式通过配置文件控制
- 不影响正常的游戏进度保存
- 关闭测试模式后恢复到实际的游戏进度
- 统一使用 Lx-y 格式，避免关卡ID混乱

## 🛠️ 开发者选项

在级别选择页面的底部，您可以找到开发者选项区域，包含：

1. **测试模式开关**
   - 快速开启/关闭测试模式
   - 实时显示当前状态

2. **重置进度按钮**
   - 清除所有游戏进度
   - 恢复到初始状态（只解锁第一关）

## 📝 使用场景

### 🧪 开发测试
- 测试新功能时无需重复通关
- 快速验证各个关卡的功能
- 检查UI在不同关卡下的表现

### 🎨 内容创作
- 快速访问高级关卡进行演示
- 制作教程或截图时的便利工具
- 体验完整的游戏内容

### 🐛 问题调试
- 重现特定关卡的问题
- 测试关卡切换逻辑
- 验证数据保存和加载

## ⚠️ 注意事项

1. **仅用于测试**: 测试模式仅用于开发和测试目的
2. **不影响正常进度**: 开启测试模式不会修改您的实际游戏进度
3. **临时状态**: 测试模式的解锁状态是临时的，关闭后恢复正常
4. **数据安全**: 您的正常游戏进度始终保存在localStorage中

## 🔧 技术实现

### 状态管理
```typescript
// 测试模式状态
const [testMode, setTestMode] = useState<boolean>(() => {
  const saved = localStorage.getItem('memorybrush-test-mode')
  return saved === 'true'
})

// 解锁状态根据测试模式动态调整
useEffect(() => {
  if (testMode) {
    // 解锁所有关卡
    setUnlockedLevels(['level-1', 'level-2', 'level-3', 'level-4'])
    setUnlockedStages([...allStages])
  } else {
    // 恢复正常进度
    const savedProgress = localStorage.getItem('memorybrush-unlocked-levels')
    setUnlockedLevels(savedProgress ? JSON.parse(savedProgress) : ['level-1'])
  }
}, [testMode])
```

### 数据持久化
- 测试模式状态: `localStorage.getItem('memorybrush-test-mode')`
- 正常游戏进度: `localStorage.getItem('memorybrush-unlocked-levels')`
- 关卡解锁状态: `localStorage.getItem('memorybrush-unlocked-stages')`

## 🚀 快速开始

1. **启动应用**:
   ```bash
   cd MemoryBrush/frontend
   npm run dev
   ```

2. **访问游戏**: http://localhost:5173

3. **开启测试模式**: 在级别选择页面找到开发者选项，开启测试模式

4. **开始测试**: 现在可以自由访问所有关卡进行测试

## 🔄 恢复正常模式

当测试完成后，记得关闭测试模式：

1. 在开发者选项中关闭测试模式开关
2. 或在控制台执行: `localStorage.removeItem('memorybrush-test-mode')`
3. 刷新页面恢复正常的游戏进度

---

🎉 **现在您可以自由地测试所有关卡功能了！**
