import { getAllStageIds, getAllLevelIds } from './levels'

// 测试模式配置
export const TEST_MODE_CONFIG = {
  // 是否启用测试模式
  // true: 解锁所有关卡，方便测试
  // false: 使用正常的关卡解锁逻辑
  enabled: true,

  // 所有级别ID
  get allLevels() {
    return getAllLevelIds()
  },

  // 所有关卡ID（统一使用 Lx-y 格式）
  get allStages() {
    return getAllStageIds()
  }
}

// 获取测试模式状态
export const isTestModeEnabled = (): boolean => {
  return TEST_MODE_CONFIG.enabled
}

// 获取所有解锁的级别
export const getAllUnlockedLevels = (): string[] => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allLevels
  }
  // 正常模式下从localStorage读取
  const saved = localStorage.getItem('memorybrush-unlocked-levels')
  return saved ? JSON.parse(saved) : ['level-1']
}

// 获取所有解锁的关卡
export const getAllUnlockedStages = (): string[] => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allStages
  }
  // 正常模式下从localStorage读取
  const saved = localStorage.getItem('memorybrush-unlocked-stages')
  return saved ? JSON.parse(saved) : ['L1-1'] // 统一使用 Lx-y 格式，默认解锁第一个关卡
}

// 获取所有解锁的描线关卡
export const getAllUnlockedTraceLevels = (): string[] => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allStages
  }
  // 正常模式下从localStorage读取
  const saved = localStorage.getItem('memorybrush-unlocked-trace-levels')
  return saved ? JSON.parse(saved) : ['L1-1']
}

// 检查级别是否解锁
export const isLevelUnlocked = (levelId: string): boolean => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allLevels.includes(levelId)
  }
  const unlockedLevels = getAllUnlockedLevels()
  return unlockedLevels.includes(levelId)
}

// 检查关卡是否解锁
export const isStageUnlocked = (stageId: string): boolean => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allStages.includes(stageId)
  }
  const unlockedStages = getAllUnlockedStages()
  return unlockedStages.includes(stageId)
}

// 检查描线关卡是否解锁
export const isTraceLevelUnlocked = (traceLevelId: string): boolean => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allStages.includes(traceLevelId)
  }
  const unlockedTraceLevels = getAllUnlockedTraceLevels()
  return unlockedTraceLevels.includes(traceLevelId)
}
