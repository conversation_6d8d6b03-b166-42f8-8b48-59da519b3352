// 统一的关卡配置
// 所有关卡都使用 Lx-y 格式，其中 x 是级别号，y 是关卡号

export interface Stage {
  id: string
  title: string
  description: string
  type: 'free-draw' | 'trace' | 'image-trace' | 'shape-straight' | 'shape-curved' | 'coming-soon'
  comingSoon?: boolean
}

export interface Level {
  id: string
  title: string
  description: string
  icon: React.ReactNode | string
  difficulty: number
  stages: Stage[]
}

export const LEVELS: Level[] = [
  {
    id: 'level-1',
    title: '线条启蒙',
    description: '从基础的线条开始，培养手部协调能力',
    icon: '📈',
    difficulty: 1,
    stages: [
      {
        id: 'L1-1',
        title: '自由画线',
        description: '随意画线，创作属于您的艺术作品',
        type: 'free-draw'
      },
      {
        id: 'L1-2',
        title: '匀速直线',
        description: '跟随引导线条，提高绘画精准度',
        type: 'image-trace'
      },
      {
        id: 'L1-3',
        title: '匀速线条组合',
        description: '从真实图片中抽取线条进行描画练习',
        type: 'image-trace'
      },
      {
        id: 'L1-4',
        title: '曲线消除',
        description: '绘制曲线和复杂图形',
        type: 'image-trace'
      },
      {
        id: 'L1-5',
        title: '直线图形',
        description: '绘制三角形、正方形、矩形等直线图形',
        type: 'shape-straight'
      }
    ]
  },
  {
    id: 'level-2',
    title: '立体空间',
    description: '从二维图形到三维立体，提升空间想象力',
    icon: '📦',
    difficulty: 2,
    stages: [
      {
        id: 'L2-1',
        title: '立体图形',
        description: '绘制锥形、立方体、圆柱体等三维图形',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L2-2',
        title: '色彩填充',
        description: '为几何图形填色，学习色系和色谱搭配',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L2-3',
        title: '质感画笔',
        description: '复杂曲线描边，选择不同质感的画笔填色',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L2-4',
        title: '阴影效果',
        description: '学习光影关系，添加阴影效果',
        type: 'coming-soon',
        comingSoon: true
      }
    ]
  },
  {
    id: 'level-3',
    title: '画面构图',
    description: '通过引导线条，完成完整的艺术画面',
    icon: '🖼️',
    difficulty: 3,
    stages: [
      {
        id: 'L3-1',
        title: '抽象艺术',
        description: '用抽象线条和色块创作现代艺术作品',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L3-2',
        title: '几何静物',
        description: '绘制几何形状组成的静物画',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L3-3',
        title: '风景艺术',
        description: '创作简单的风景画作品',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L3-4',
        title: '肖像艺术',
        description: '学习人物肖像的基本绘制',
        type: 'coming-soon',
        comingSoon: true
      }
    ]
  },
  {
    id: 'level-4',
    title: '智能创作',
    description: '上传照片，AI辅助创作个性化艺术作品',
    icon: '🤖',
    difficulty: 4,
    stages: [
      {
        id: 'L4-1',
        title: '照片描边',
        description: '上传照片，提取轮廓进行描边练习',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L4-2',
        title: '风格渲染',
        description: '选择不同艺术风格，AI辅助渲染作品',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L4-3',
        title: 'AI协作',
        description: '与AI协作，创作独特的个人艺术作品',
        type: 'coming-soon',
        comingSoon: true
      },
      {
        id: 'L4-4',
        title: '创意模式',
        description: '自由创作模式，发挥无限想象力',
        type: 'coming-soon',
        comingSoon: true
      }
    ]
  }
]

// 获取所有关卡ID
export const getAllStageIds = (): string[] => {
  return LEVELS.flatMap(level => level.stages.map(stage => stage.id))
}

// 获取所有级别ID
export const getAllLevelIds = (): string[] => {
  return LEVELS.map(level => level.id)
}

// 根据关卡ID获取关卡信息
export const getStageById = (stageId: string): Stage | undefined => {
  for (const level of LEVELS) {
    const stage = level.stages.find(s => s.id === stageId)
    if (stage) return stage
  }
  return undefined
}

// 根据级别ID获取级别信息
export const getLevelById = (levelId: string): Level | undefined => {
  return LEVELS.find(level => level.id === levelId)
}

// 根据关卡ID获取所属级别
export const getLevelByStageId = (stageId: string): Level | undefined => {
  for (const level of LEVELS) {
    if (level.stages.some(stage => stage.id === stageId)) {
      return level
    }
  }
  return undefined
}
