import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * 动态背景Hook - 根据当前路由设置对应关卡的背景图片
 */
export const useDynamicBackground = () => {
  const location = useLocation();

  useEffect(() => {
    // 解析当前路径，提取关卡信息
    const pathParts = location.pathname.split('/').filter(part => part.length > 0);
    let levelStage = '';

    // 检查是否是Level 1的关卡选择页面 /game/level-1
    if (pathParts[0] === 'game' && pathParts[1] === 'level-1' && pathParts.length === 2) {
      // Level 1关卡选择页面，清除背景让组件自己处理
      document.body.style.backgroundImage = '';
      document.documentElement.style.backgroundImage = '';
      return;
    }

    // 检查是否是游戏页面路径 /game/L1/2 或 /game/level-1/stage-2
    if (pathParts[0] === 'game' && pathParts.length >= 3) {
      const level = pathParts[1];
      const stage = pathParts[2];

      // 转换为标准格式 L1-2
      if (stage.startsWith('L') && stage.includes('-')) {
        // stage已经是完整格式 L1-2，直接使用
        levelStage = stage;
      } else if (level.startsWith('level-') && stage.startsWith('stage-')) {
        // 转换 level-1/stage-2 为 L1-2
        const levelNum = level.replace('level-', '');
        const stageNum = stage.replace('stage-', '');
        levelStage = `L${levelNum}-${stageNum}`;
      } else if (level.startsWith('L') && !level.includes('-')) {
        // L1/2 格式
        levelStage = `${level}-${stage}`;
      } else if (level.startsWith('level-') && stage.startsWith('L')) {
        // level-1/L1-2 格式，直接使用stage部分
        levelStage = stage;
      }
    }

    // 设置背景图片
    const setBackgroundImage = (imagePath: string) => {
      document.body.style.backgroundImage = `url('${imagePath}')`;
      document.documentElement.style.backgroundImage = `url('${imagePath}')`;
    };

    // 尝试加载对应关卡的背景图片
    const loadLevelBackground = async (levelStage: string) => {
      // 检查是否是L1-x关卡
      if (levelStage.startsWith('L1-')) {
        const formats = ['jpg', 'jpeg', 'png'];

        for (const format of formats) {
          try {
            const imagePath = `/${levelStage}/background.${format}`;

            // 测试图片是否存在
            const img = new Image();
            await new Promise((resolve, reject) => {
              img.onload = resolve;
              img.onerror = reject;
              img.src = imagePath;
            });

            // 图片加载成功，设置为背景
            setBackgroundImage(imagePath);
            console.log('✅ 成功加载L1关卡背景:', imagePath);
            return;
          } catch (error) {
            // 继续尝试下一个格式
            continue;
          }
        }

        // L1关卡没有找到对应背景，使用统一背景
        setBackgroundImage('/background.jpg');
        console.log('⚠️ L1关卡背景不存在，使用统一背景: /background.jpg');
      } else {
        // 非L1关卡，直接使用统一背景
        setBackgroundImage('/background.jpg');
        console.log('✅ 使用统一背景:', '/background.jpg');
      }
    };

    if (levelStage) {
      loadLevelBackground(levelStage);
    } else {
      // 非游戏页面，使用统一背景
      setBackgroundImage('/background.jpg');
      console.log('✅ 非游戏页面使用统一背景:', '/background.jpg');
    }

    // 清理函数
    return () => {
      // 组件卸载时不需要清理背景，因为背景应该保持
    };
  }, [location.pathname]);
};

export default useDynamicBackground;
