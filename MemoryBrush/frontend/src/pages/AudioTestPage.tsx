import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Select, message } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, StopOutlined } from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

const AudioTestPage: React.FC = () => {
    const [selectedLevel, setSelectedLevel] = useState<string>('L1-2');
    const [startAudio, setStartAudio] = useState<HTMLAudioElement | null>(null);
    const [endAudio, setEndAudio] = useState<HTMLAudioElement | null>(null);
    const [isStartPlaying, setIsStartPlaying] = useState(false);
    const [isEndPlaying, setIsEndPlaying] = useState(false);
    const [startText, setStartText] = useState<string>('');
    const [endText, setEndText] = useState<string>('');
    const [displayedStartText, setDisplayedStartText] = useState<string>('');
    const [displayedEndText, setDisplayedEndText] = useState<string>('');
    const [isTypingStart, setIsTypingStart] = useState(false);
    const [isTypingEnd, setIsTypingEnd] = useState(false);

    // 可用的关卡列表
    const availableLevels = ['L1-1', 'L1-2', 'L1-3', 'L1-4', 'L1-5'];

    // 加载语音和文本文件
    useEffect(() => {
        const loadAudioAndText = async () => {
            // 清理之前的音频
            if (startAudio) {
                startAudio.pause();
                startAudio.src = '';
            }
            if (endAudio) {
                endAudio.pause();
                endAudio.src = '';
            }

            // 加载开始语音
            const startAudioElement = new Audio();
            startAudioElement.addEventListener('ended', () => setIsStartPlaying(false));
            startAudioElement.addEventListener('error', () => {
                console.log(`未找到开始语音: ${selectedLevel}/start.mp3`);
            });
            startAudioElement.src = `/${selectedLevel}/start.mp3`;
            setStartAudio(startAudioElement);

            // 加载结束语音
            const endAudioElement = new Audio();
            endAudioElement.addEventListener('ended', () => setIsEndPlaying(false));
            endAudioElement.addEventListener('error', () => {
                console.log(`未找到结束语音: ${selectedLevel}/end.mp3`);
            });
            endAudioElement.src = `/${selectedLevel}/end.mp3`;
            setEndAudio(endAudioElement);

            // 加载开始文本
            try {
                const startResponse = await fetch(`/${selectedLevel}/start.txt`);
                if (startResponse.ok) {
                    const text = await startResponse.text();
                    setStartText(text.trim());
                } else {
                    setStartText('未找到开始引导文本');
                }
            } catch (error) {
                setStartText('加载开始文本失败');
            }

            // 加载结束文本
            try {
                const endResponse = await fetch(`/${selectedLevel}/end.txt`);
                if (endResponse.ok) {
                    const text = await endResponse.text();
                    setEndText(text.trim());
                } else {
                    setEndText('未找到结束鼓励文本');
                }
            } catch (error) {
                setEndText('加载结束文本失败');
            }
        };

        loadAudioAndText();
    }, [selectedLevel]);

    // 播放开始语音和打字效果
    const playStartAudio = () => {
        if (startAudio && !isStartPlaying) {
            setIsStartPlaying(true);
            setIsTypingStart(true);
            setDisplayedStartText('');
            
            // 开始播放语音
            startAudio.play().catch(error => {
                console.error('播放开始语音失败:', error);
                message.error('播放语音失败，请检查文件是否存在');
                setIsStartPlaying(false);
            });

            // 开始打字效果
            let currentIndex = 0;
            const typingSpeed = 100;

            const typeNextCharacter = () => {
                if (currentIndex < startText.length) {
                    setDisplayedStartText(startText.slice(0, currentIndex + 1));
                    currentIndex++;
                    setTimeout(typeNextCharacter, typingSpeed);
                } else {
                    setIsTypingStart(false);
                }
            };

            typeNextCharacter();
        }
    };

    // 播放结束语音和打字效果
    const playEndAudio = () => {
        if (endAudio && !isEndPlaying) {
            setIsEndPlaying(true);
            setIsTypingEnd(true);
            setDisplayedEndText('');
            
            // 开始播放语音
            endAudio.play().catch(error => {
                console.error('播放结束语音失败:', error);
                message.error('播放语音失败，请检查文件是否存在');
                setIsEndPlaying(false);
            });

            // 开始打字效果
            let currentIndex = 0;
            const typingSpeed = 80;

            const typeNextCharacter = () => {
                if (currentIndex < endText.length) {
                    setDisplayedEndText(endText.slice(0, currentIndex + 1));
                    currentIndex++;
                    setTimeout(typeNextCharacter, typingSpeed);
                } else {
                    setIsTypingEnd(false);
                }
            };

            typeNextCharacter();
        }
    };

    // 停止播放
    const stopAudio = (type: 'start' | 'end') => {
        if (type === 'start' && startAudio) {
            startAudio.pause();
            startAudio.currentTime = 0;
            setIsStartPlaying(false);
            setIsTypingStart(false);
        } else if (type === 'end' && endAudio) {
            endAudio.pause();
            endAudio.currentTime = 0;
            setIsEndPlaying(false);
            setIsTypingEnd(false);
        }
    };

    return (
        <div className="min-h-screen p-8" style={{ background: 'transparent' }}>
            <div className="max-w-4xl mx-auto">
                <Title level={1} className="text-center mb-8">语音引导功能测试</Title>
                
                <Card className="mb-6">
                    <Title level={3}>选择关卡</Title>
                    <Select
                        value={selectedLevel}
                        onChange={setSelectedLevel}
                        style={{ width: 200 }}
                        size="large"
                    >
                        {availableLevels.map(level => (
                            <Option key={level} value={level}>{level}</Option>
                        ))}
                    </Select>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* 开始引导语测试 */}
                    <Card>
                        <Title level={3}>开始引导语</Title>
                        <div className="mb-4">
                            <Text strong>文本内容：</Text>
                            <div className="bg-gray-100 p-3 rounded mt-2 min-h-[100px]">
                                {startText || '加载中...'}
                            </div>
                        </div>
                        
                        <div className="mb-4">
                            <Text strong>打字效果预览：</Text>
                            <div className="bg-blue-50 p-3 rounded mt-2 min-h-[100px] border-2 border-blue-200">
                                {displayedStartText}
                                {isTypingStart && <span className="animate-pulse">|</span>}
                            </div>
                        </div>

                        <Space>
                            <Button
                                type="primary"
                                icon={<PlayCircleOutlined />}
                                onClick={playStartAudio}
                                disabled={isStartPlaying}
                                size="large"
                            >
                                播放开始语音
                            </Button>
                            <Button
                                icon={<StopOutlined />}
                                onClick={() => stopAudio('start')}
                                disabled={!isStartPlaying}
                                size="large"
                            >
                                停止
                            </Button>
                        </Space>
                        
                        <div className="mt-2">
                            <Text type={isStartPlaying ? 'success' : 'secondary'}>
                                状态: {isStartPlaying ? '正在播放' : '已停止'}
                            </Text>
                        </div>
                    </Card>

                    {/* 结束鼓励语测试 */}
                    <Card>
                        <Title level={3}>结束鼓励语</Title>
                        <div className="mb-4">
                            <Text strong>文本内容：</Text>
                            <div className="bg-gray-100 p-3 rounded mt-2 min-h-[100px]">
                                {endText || '加载中...'}
                            </div>
                        </div>
                        
                        <div className="mb-4">
                            <Text strong>打字效果预览：</Text>
                            <div className="bg-green-50 p-3 rounded mt-2 min-h-[100px] border-2 border-green-200">
                                🎉 {displayedEndText}
                                {isTypingEnd && <span className="animate-pulse">|</span>}
                            </div>
                        </div>

                        <Space>
                            <Button
                                type="primary"
                                icon={<PlayCircleOutlined />}
                                onClick={playEndAudio}
                                disabled={isEndPlaying}
                                size="large"
                            >
                                播放结束语音
                            </Button>
                            <Button
                                icon={<StopOutlined />}
                                onClick={() => stopAudio('end')}
                                disabled={!isEndPlaying}
                                size="large"
                            >
                                停止
                            </Button>
                        </Space>
                        
                        <div className="mt-2">
                            <Text type={isEndPlaying ? 'success' : 'secondary'}>
                                状态: {isEndPlaying ? '正在播放' : '已停止'}
                            </Text>
                        </div>
                    </Card>
                </div>

                <Card className="mt-6">
                    <Title level={3}>使用说明</Title>
                    <Paragraph>
                        1. 选择不同的关卡来测试对应的语音文件<br/>
                        2. 点击"播放开始语音"测试开始引导语的语音和打字效果<br/>
                        3. 点击"播放结束语音"测试结束鼓励语的语音和打字效果<br/>
                        4. 语音和文字会同时开始，文字打字完成后会保持显示直到语音播放完毕<br/>
                        5. 如果没有对应的语音文件，只会显示打字效果
                    </Paragraph>
                </Card>
            </div>
        </div>
    );
};

export default AudioTestPage;
