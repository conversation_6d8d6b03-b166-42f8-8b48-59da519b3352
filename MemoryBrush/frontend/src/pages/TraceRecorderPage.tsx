import React, { useState } from 'react';
import { Tabs, Card } from 'antd';
import { motion } from 'framer-motion';
import TraceRecorder from '@components/TraceRecorder';
import TraceManager from '@components/TraceManager';
import GuidePreview from '@components/GuidePreview';
import { GuidePath } from '../services/traceService';

const TraceRecorderPage: React.FC = () => {
    const [activeTab, setActiveTab] = useState('recorder');
    const [generatedGuidePaths, setGeneratedGuidePaths] = useState<GuidePath[]>([]);
    const [guideCanvasSize, setGuideCanvasSize] = useState({ width: 800, height: 600 });
    const [guideBackgroundImage, setGuideBackgroundImage] = useState<string | undefined>();

    // 处理生成的引导线
    const handleGenerateGuide = (
        guidePaths: GuidePath[],
        canvasSize: { width: number; height: number },
        backgroundImage?: string
    ) => {
        setGeneratedGuidePaths(guidePaths);
        setGuideCanvasSize(canvasSize);
        setGuideBackgroundImage(backgroundImage);
        setActiveTab('preview'); // 自动切换到预览标签
    };

    return (
        <div className="trace-recorder-page min-h-screen" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
            <div className="container mx-auto px-4 py-6">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                >
                    <Card className="shadow-lg">
                        <Tabs
                            activeKey={activeTab}
                            onChange={setActiveTab}
                            size="large"
                            className="trace-tabs"
                            items={[
                                {
                                    key: 'recorder',
                                    label: '📝 轨迹记录',
                                    children: <TraceRecorder />
                                },
                                {
                                    key: 'manager',
                                    label: '📁 轨迹管理',
                                    children: <TraceManager onGenerateGuide={handleGenerateGuide} />
                                },
                                {
                                    key: 'preview',
                                    label: `🎯 引导线预览 ${generatedGuidePaths.length > 0 ? `(${generatedGuidePaths.length})` : ''}`,
                                    children: (
                                        <GuidePreview
                                            guidePaths={generatedGuidePaths}
                                            canvasSize={guideCanvasSize}
                                            backgroundImage={guideBackgroundImage}
                                        />
                                    )
                                }
                            ]}
                        />
                    </Card>
                </motion.div>
            </div>
        </div>
    );
};

export default TraceRecorderPage;
