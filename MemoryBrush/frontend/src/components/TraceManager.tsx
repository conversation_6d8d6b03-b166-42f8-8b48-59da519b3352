import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
    Card, 
    Typography, 
    Table, 
    Button, 
    Space, 
    Modal, 
    message, 
    Popconfirm,
    Select,
    InputNumber,
    Divider,
    Tag,
    Tooltip
} from "antd";
import { 
    EyeOutlined, 
    DeleteOutlined, 
    DownloadOutlined,
    UploadOutlined,
    PlayCircleOutlined,
    // SettingOutlined,
    ReloadOutlined
} from "@ant-design/icons";
import traceService, { TraceSession, GuidePath } from "../services/traceService";

const { Title, Text } = Typography;
const { Option } = Select;

interface TraceManagerProps {
    onGenerateGuide?: (
        guidePaths: GuidePath[],
        canvasSize: { width: number; height: number },
        backgroundImage?: string
    ) => void;
}

const TraceManager: React.FC<TraceManagerProps> = ({ onGenerateGuide }) => {
    const [sessions, setSessions] = useState<TraceSession[]>([]);
    const [loading, setLoading] = useState(false);
    const [previewSession, setPreviewSession] = useState<TraceSession | null>(null);
    const [previewModalVisible, setPreviewModalVisible] = useState(false);
    const [generateModalVisible, setGenerateModalVisible] = useState(false);
    const [selectedSession, setSelectedSession] = useState<TraceSession | null>(null);
    const [generateOptions, setGenerateOptions] = useState({
        simplification_level: 'medium' as 'low' | 'medium' | 'high',
        min_stroke_length: 20,
        merge_distance: 50
    });
    const [generating, setGenerating] = useState(false);

    // 加载轨迹会话列表
    const loadSessions = async () => {
        setLoading(true);
        try {
            let allSessions: TraceSession[] = [];

            // 尝试从API加载
            try {
                const apiSessions = await traceService.getSessions();
                allSessions = [...apiSessions];
                console.log("从API加载了", apiSessions.length, "个会话");
            } catch (apiError) {
                console.warn("API加载失败:", apiError);
            }

            // 从本地存储加载
            try {
                const localSessions = traceService.getFromLocalStorage();
                console.log("从本地存储加载了", localSessions.length, "个会话");

                // 合并数据，避免重复（基于ID去重）
                const existingIds = new Set(allSessions.map(s => s.id));
                const uniqueLocalSessions = localSessions.filter(s => !existingIds.has(s.id));
                allSessions = [...allSessions, ...uniqueLocalSessions];
            } catch (localError) {
                console.warn("本地存储加载失败:", localError);
            }

            // 按创建时间排序（最新的在前）
            allSessions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

            setSessions(allSessions);
            console.log("总共加载了", allSessions.length, "个会话");
        } catch (error) {
            console.error("加载轨迹会话失败:", error);
            message.error("加载轨迹会话失败");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadSessions();
    }, []);

    // 删除会话
    const handleDelete = async (sessionId: string) => {
        try {
            // 尝试从API删除
            try {
                await traceService.deleteSession(sessionId);
            } catch (apiError) {
                console.warn("API删除失败，从本地存储删除:", apiError);
                // API失败时从本地存储删除
                traceService.deleteFromLocalStorage(sessionId);
            }
            
            message.success("会话删除成功");
            loadSessions(); // 重新加载列表
        } catch (error) {
            console.error("删除会话失败:", error);
            message.error("删除会话失败");
        }
    };

    // 预览会话
    const handlePreview = (session: TraceSession) => {
        setPreviewSession(session);
        setPreviewModalVisible(true);
    };

    // 导出会话
    const handleExport = (session: TraceSession) => {
        traceService.exportSessionAsJson(session);
        message.success("会话数据已导出");
    };

    // 导入会话
    const handleImport = () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (!file) return;

            try {
                const session = await traceService.importSessionFromJson(file);
                
                // 尝试保存到API
                try {
                    await traceService.createSession({
                        name: session.name,
                        description: session.description,
                        strokes: session.strokes,
                        canvasSize: session.canvasSize,
                        // backgroundImage: session.backgroundImage,
                        duration: session.duration
                    });
                } catch (apiError) {
                    console.warn("API保存失败，保存到本地存储:", apiError);
                    traceService.saveToLocalStorage(session);
                }
                
                message.success("会话导入成功");
                loadSessions();
            } catch (error) {
                console.error("导入会话失败:", error);
                message.error("导入会话失败: " + (error as Error).message);
            }
        };
        input.click();
    };

    // 生成引导线
    const handleGenerateGuide = (session: TraceSession) => {
        setSelectedSession(session);
        setGenerateModalVisible(true);
    };

    // 确认生成引导线
    const confirmGenerateGuide = async () => {
        if (!selectedSession) return;

        setGenerating(true);
        try {
            // 检查会话是否存在于API中
            let sessionToUse = selectedSession;
            let needsUpload = false;

            try {
                // 尝试从API获取会话
                await traceService.getSession(selectedSession.id);
            } catch (apiError) {
                // 会话不存在于API中，需要先上传
                console.log("会话不存在于API中，尝试上传...");
                needsUpload = true;
            }

            // 如果需要上传，先将会话保存到API
            if (needsUpload) {
                try {
                    const uploadResponse = await traceService.createSession({
                        name: selectedSession.name,
                        description: selectedSession.description,
                        strokes: selectedSession.strokes,
                        canvasSize: selectedSession.canvasSize,
                        // backgroundImage: selectedSession.backgroundImage,
                        duration: selectedSession.duration
                    });

                    if (uploadResponse.success && uploadResponse.data) {
                        sessionToUse = uploadResponse.data;
                        console.log("会话上传成功，新ID:", sessionToUse.id);
                    } else {
                        throw new Error("上传会话失败");
                    }
                } catch (uploadError) {
                    console.error("上传会话失败:", uploadError);
                    message.error("无法上传会话到服务器，请检查网络连接");
                    return;
                }
            }

            // 生成引导线
            const response = await traceService.generateGuidePaths(
                sessionToUse.id,
                generateOptions
            );

            if (response.success && response.guide_paths) {
                message.success("引导线生成成功");
                setGenerateModalVisible(false);

                // 调用回调函数，传递生成的引导线
                if (onGenerateGuide && response.canvas_size) {
                    onGenerateGuide(
                        response.guide_paths,
                        response.canvas_size,
                        // sessionToUse.backgroundImage
                        undefined
                    );
                }

                // 如果上传了新会话，刷新会话列表
                if (needsUpload) {
                    loadSessions();
                }
            } else {
                throw new Error(response.message || "生成引导线失败");
            }
        } catch (error) {
            console.error("生成引导线失败:", error);
            message.error("生成引导线失败: " + (error as Error).message);
        } finally {
            setGenerating(false);
        }
    };

    // 格式化时长
    const formatDuration = (duration: number) => {
        const seconds = Math.floor(duration / 1000);
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    // 计算会话统计
    const getSessionStats = (session: TraceSession) => {
        return traceService.calculateSessionStats(session);
    };

    // 表格列定义
    const columns = [
        {
            title: '会话名称',
            dataIndex: 'name',
            key: 'name',
            render: (text: string, record: TraceSession) => (
                <div>
                    <Text strong>{text}</Text>
                    {record.description && (
                        <div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                                {record.description}
                            </Text>
                        </div>
                    )}
                </div>
            ),
        },
        {
            title: '统计信息',
            key: 'stats',
            render: (_: any, record: TraceSession) => {
                const stats = getSessionStats(record);
                return (
                    <Space direction="vertical" size="small">
                        <Tag color="blue">{stats.totalStrokes} 笔画</Tag>
                        <Tag color="green">{stats.totalPoints} 点</Tag>
                        <Tag color="orange">{formatDuration(stats.duration)}</Tag>
                    </Space>
                );
            },
        },
        {
            title: '创建时间',
            dataIndex: 'createdAt',
            key: 'createdAt',
            render: (text: string) => new Date(text).toLocaleString(),
        },
        {
            title: '操作',
            key: 'actions',
            render: (_: any, record: TraceSession) => (
                <Space>
                    <Tooltip title="预览">
                        <Button
                            icon={<EyeOutlined />}
                            onClick={() => handlePreview(record)}
                            size="small"
                        />
                    </Tooltip>
                    <Tooltip title="生成引导线">
                        <Button
                            icon={<PlayCircleOutlined />}
                            onClick={() => handleGenerateGuide(record)}
                            size="small"
                            type="primary"
                        />
                    </Tooltip>
                    <Tooltip title="导出">
                        <Button
                            icon={<DownloadOutlined />}
                            onClick={() => handleExport(record)}
                            size="small"
                        />
                    </Tooltip>
                    <Tooltip title="删除">
                        <Popconfirm
                            title="确定要删除这个会话吗？"
                            onConfirm={() => handleDelete(record.id)}
                            okText="确定"
                            cancelText="取消"
                        >
                            <Button
                                icon={<DeleteOutlined />}
                                danger
                                size="small"
                            />
                        </Popconfirm>
                    </Tooltip>
                </Space>
            ),
        },
    ];

    return (
        <div className="trace-manager">
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
            >
                <Card>
                    <div className="flex justify-between items-center mb-6">
                        <Title level={3}>轨迹管理</Title>
                        <Space>
                            <Button
                                icon={<ReloadOutlined />}
                                onClick={loadSessions}
                                loading={loading}
                            >
                                刷新
                            </Button>
                            <Button
                                icon={<UploadOutlined />}
                                onClick={handleImport}
                            >
                                导入会话
                            </Button>
                        </Space>
                    </div>

                    <Table
                        columns={columns}
                        dataSource={sessions}
                        rowKey="id"
                        loading={loading}
                        pagination={{
                            pageSize: 10,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total) => `共 ${total} 个会话`,
                        }}
                    />
                </Card>
            </motion.div>

            {/* 预览模态框 */}
            <Modal
                title={`预览会话: ${previewSession?.name}`}
                open={previewModalVisible}
                onCancel={() => setPreviewModalVisible(false)}
                footer={null}
                width={800}
            >
                {previewSession && (
                    <div>
                        <Space direction="vertical" className="w-full">
                            <div>
                                <Text strong>描述: </Text>
                                <Text>{previewSession.description || "无"}</Text>
                            </div>
                            <div>
                                <Text strong>画布尺寸: </Text>
                                <Text>{previewSession.canvasSize.width} × {previewSession.canvasSize.height}</Text>
                            </div>
                            <div>
                                <Text strong>背景图: </Text>
                                <Text>{"无"}</Text>
                                {/* {previewSession.backgroundImage && (
                                    <div className="mt-2">
                                        <img
                                            src={previewSession.backgroundImage}
                                            alt="背景图预览"
                                            style={{
                                                maxWidth: "200px",
                                                maxHeight: "150px",
                                                border: "1px solid #d9d9d9",
                                                borderRadius: "4px"
                                            }}
                                        />
                                    </div>
                                )} */}
                            </div>
                            <div>
                                <Text strong>统计信息: </Text>
                                {(() => {
                                    const stats = getSessionStats(previewSession);
                                    return (
                                        <div>
                                            <Tag color="blue">{stats.totalStrokes} 笔画</Tag>
                                            <Tag color="green">{stats.totalPoints} 点</Tag>
                                            <Tag color="orange">{formatDuration(stats.duration)}</Tag>
                                            <Tag color="purple">{Math.round(stats.totalLength)} 像素长度</Tag>
                                        </div>
                                    );
                                })()}
                            </div>
                        </Space>
                    </div>
                )}
            </Modal>

            {/* 生成引导线配置模态框 */}
            <Modal
                title={`生成引导线: ${selectedSession?.name}`}
                open={generateModalVisible}
                onCancel={() => setGenerateModalVisible(false)}
                onOk={confirmGenerateGuide}
                confirmLoading={generating}
                okText="生成"
                cancelText="取消"
            >
                <Space direction="vertical" className="w-full">
                    <div>
                        <Text strong>简化级别:</Text>
                        <Select
                            value={generateOptions.simplification_level}
                            onChange={(value) => setGenerateOptions(prev => ({ ...prev, simplification_level: value }))}
                            className="w-full mt-2"
                        >
                            <Option value="low">低 (保留更多细节)</Option>
                            <Option value="medium">中 (平衡)</Option>
                            <Option value="high">高 (更简化)</Option>
                        </Select>
                    </div>
                    
                    <div>
                        <Text strong>最小笔画长度 (像素):</Text>
                        <InputNumber
                            value={generateOptions.min_stroke_length}
                            onChange={(value) => setGenerateOptions(prev => ({ ...prev, min_stroke_length: value || 20 }))}
                            min={1}
                            max={200}
                            className="w-full mt-2"
                        />
                    </div>
                    
                    <div>
                        <Text strong>合并距离 (像素):</Text>
                        <InputNumber
                            value={generateOptions.merge_distance}
                            onChange={(value) => setGenerateOptions(prev => ({ ...prev, merge_distance: value || 50 }))}
                            min={1}
                            max={200}
                            className="w-full mt-2"
                        />
                    </div>
                    
                    <Divider />
                    
                    <Text type="secondary">
                        这些设置将影响生成的引导线的复杂度和精度。较高的简化级别会产生更简单的引导线，
                        较大的合并距离会将相近的笔画合并为一条路径。
                    </Text>
                </Space>
            </Modal>
        </div>
    );
};

export default TraceManager;
