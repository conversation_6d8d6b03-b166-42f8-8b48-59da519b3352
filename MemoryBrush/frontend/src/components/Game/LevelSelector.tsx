import React from 'react'
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, <PERSON>, Col, Popconfirm } from 'antd'
import { motion } from 'framer-motion'
import {
  LineChartOutlined,
  PlayCircleOutlined,
  StarOutlined,
  BoxPlotOutlined,
  PictureOutlined,
  LockOutlined,
  ReloadOutlined
} from '@ant-design/icons'

// 统一的关卡配置
import { LEVELS } from '@/config/levels'

const { Title, Text } = Typography

interface LevelSelectorProps {
  onSelectLevel: (levelId: string) => void
  unlockedLevels?: string[]
  onResetProgress?: () => void
}

const LevelSelector: React.FC<LevelSelectorProps> = ({ onSelectLevel, unlockedLevels = ['level-1'], onResetProgress }) => {
  // 使用统一的关卡配置，并添加图标
  const levels = LEVELS.map(level => ({
    ...level,
    icon: level.id === 'level-1' ? <LineChartOutlined className="text-4xl" /> :
          level.id === 'level-2' ? <BoxPlotOutlined className="text-4xl" /> :
          level.id === 'level-3' ? <PictureOutlined className="text-4xl" /> :
          level.id === 'level-4' ? <StarOutlined className="text-4xl" /> :
          <PlayCircleOutlined className="text-4xl" />
  }))


  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1: return 'text-green-600 bg-green-100'
      case 2: return 'text-blue-600 bg-blue-100'
      case 3: return 'text-purple-600 bg-purple-100'
      case 4: return 'text-orange-600 bg-orange-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1: return '入门'
      case 2: return '初级'
      case 3: return '中级'
      case 4: return '高级'
      default: return '未知'
    }
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-8"
      >
        <Title level={1} className="text-purple-600 mb-4">
          选择游戏级别
        </Title>
        <Text className="text-xl text-gray-600">
          从简单的线条开始，逐步提升您的绘画技能
        </Text>
      </motion.div>

      <Row gutter={[24, 24]}>
        {levels.map((level, levelIndex) => {
          const isLevelUnlocked = unlockedLevels.includes(level.id)

          return (
            <Col xs={24} lg={12} key={level.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: levelIndex * 0.1 }}
              >
                <Card
                  className={`h-full shadow-lg transition-all duration-300 ${
                    isLevelUnlocked
                      ? 'hover:shadow-xl cursor-pointer'
                      : 'opacity-60 cursor-not-allowed'
                  }`}
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="text-center mb-6">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white relative ${
                      isLevelUnlocked
                        ? 'bg-gradient-to-br from-purple-400 to-purple-600'
                        : 'bg-gradient-to-br from-gray-400 to-gray-500'
                    }`}>
                      {!isLevelUnlocked && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <LockOutlined className="text-2xl" />
                        </div>
                      )}
                      {isLevelUnlocked && level.icon}
                    </div>

                    <Title level={3} className={`mb-2 ${!isLevelUnlocked ? 'text-gray-500' : ''}`}>
                      {level.title}
                    </Title>

                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-3 ${
                      isLevelUnlocked ? getDifficultyColor(level.difficulty) : 'bg-gray-100 text-gray-500'
                    }`}>
                      <StarOutlined className="mr-1" />
                      {getDifficultyText(level.difficulty)}
                    </div>

                    <Text className={`block ${!isLevelUnlocked ? 'text-gray-500' : 'text-gray-600'}`}>
                      {isLevelUnlocked ? level.description : '完成前面的级别来解锁'}
                    </Text>
                  </div>

                  {/* 进入级别按钮 */}
                  <div className="mt-6">
                    <motion.div
                      whileHover={isLevelUnlocked ? { scale: 1.02 } : {}}
                      whileTap={isLevelUnlocked ? { scale: 0.98 } : {}}
                    >
                      <Button
                        type={isLevelUnlocked ? "primary" : "default"}
                        size="large"
                        icon={isLevelUnlocked ? <PlayCircleOutlined /> : <LockOutlined />}
                        onClick={() => isLevelUnlocked && onSelectLevel(level.id)}
                        disabled={!isLevelUnlocked}
                        className="w-full h-12 text-lg"
                      >
                        {isLevelUnlocked ? '进入级别' : '级别锁定'}
                      </Button>
                    </motion.div>
                  </div>
                </Card>
              </motion.div>
            </Col>
          )
        })}
      </Row>

      {/* 开发者选项 */}
      {onResetProgress && (
        <div className="text-center mt-8">
          <Popconfirm
            title="重置游戏进度"
            description="确定要重置所有游戏进度吗？这将清除所有解锁的级别。"
            onConfirm={onResetProgress}
            okText="确定"
            cancelText="取消"
          >
            <Button
              icon={<ReloadOutlined />}
              size="small"
              className="text-gray-500 hover:text-gray-700"
            >
              重置进度
            </Button>
          </Popconfirm>
        </div>
      )}
    </div>
  )
}

export default LevelSelector
