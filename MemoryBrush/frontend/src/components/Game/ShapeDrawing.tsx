import React, { useState } from 'react'
import { Card, Typography, Button, Space, Progress, message, Tag } from 'antd'
import { motion } from 'framer-motion'
import { ArrowLeftOutlined, CheckOutlined, ReloadOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import DrawingCanvas from './DrawingCanvas'

const { Title, Text } = Typography

interface ShapeDrawingProps {
  onBack: () => void
  onComplete: (score: number) => void
  shapeType: 'straight' | 'curved'
}

interface Shape {
  id: string
  name: string
  description: string
  instruction: string
  difficulty: number
  type: 'straight' | 'curved'
}

const shapes: Shape[] = [
  // 直线图形
  {
    id: 'triangle',
    name: '三角形',
    description: '绘制一个三角形',
    instruction: '画三条直线，让它们相互连接形成三角形',
    difficulty: 1,
    type: 'straight'
  },
  {
    id: 'square',
    name: '正方形',
    description: '绘制一个正方形',
    instruction: '画四条相等的直线，形成四个直角',
    difficulty: 1,
    type: 'straight'
  },
  {
    id: 'rectangle',
    name: '长方形',
    description: '绘制一个长方形',
    instruction: '画四条直线，对边相等且平行',
    difficulty: 1,
    type: 'straight'
  },
  {
    id: 'pentagon',
    name: '五边形',
    description: '绘制一个五边形',
    instruction: '画五条直线，形成一个封闭的五边形',
    difficulty: 2,
    type: 'straight'
  },
  {
    id: 'hexagon',
    name: '六边形',
    description: '绘制一个六边形',
    instruction: '画六条直线，形成一个封闭的六边形',
    difficulty: 2,
    type: 'straight'
  },
  // 曲线图形
  {
    id: 'circle',
    name: '圆形',
    description: '绘制一个圆形',
    instruction: '画一条连续的曲线，让起点和终点相接',
    difficulty: 1,
    type: 'curved'
  },
  {
    id: 'oval',
    name: '椭圆形',
    description: '绘制一个椭圆形',
    instruction: '画一个拉长的圆形，像鸡蛋的形状',
    difficulty: 2,
    type: 'curved'
  },
  {
    id: 'heart',
    name: '心形',
    description: '绘制一个心形',
    instruction: '画两个圆弧在顶部，底部汇聚成一个点',
    difficulty: 3,
    type: 'curved'
  },
  {
    id: 'star',
    name: '星形',
    description: '绘制一个五角星',
    instruction: '画五个尖角，每个尖角之间用直线或曲线连接',
    difficulty: 3,
    type: 'curved'
  }
]

const ShapeDrawing: React.FC<ShapeDrawingProps> = ({ onBack, onComplete, shapeType }) => {
  const [currentShapeIndex, setCurrentShapeIndex] = useState(0)
  const [isCompleted, setIsCompleted] = useState(false)
  const [score, setScore] = useState(0)
  const [showHint, setShowHint] = useState(false)

  const filteredShapes = shapes.filter(shape => shape.type === shapeType)
  const currentShape = filteredShapes[currentShapeIndex]

  const handleSave = (_imageData: string) => {
    // 简单的评分算法（实际项目中可以实现形状识别）
    const baseScore = 60
    const difficultyBonus = (4 - currentShape.difficulty) * 10
    const randomBonus = Math.floor(Math.random() * 20)
    const finalScore = Math.min(100, baseScore + difficultyBonus + randomBonus)
    
    setScore(finalScore)
    setIsCompleted(true)
    message.success(`太棒了！您的${currentShape.name}得分：${finalScore}分`)
  }

  const handleComplete = () => {
    onComplete(score)
  }

  const handleNextShape = () => {
    if (currentShapeIndex < filteredShapes.length - 1) {
      setCurrentShapeIndex(prev => prev + 1)
      setIsCompleted(false)
      setScore(0)
      setShowHint(false)
    } else {
      handleComplete()
    }
  }

  const handleRestart = () => {
    setIsCompleted(false)
    setScore(0)
  }

  const toggleHint = () => {
    setShowHint(!showHint)
  }

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1: return 'green'
      case 2: return 'blue'
      case 3: return 'purple'
      default: return 'gray'
    }
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1: return '简单'
      case 2: return '中等'
      case 3: return '困难'
      default: return '未知'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* 标题区域 */}
          <Card className="mb-6 shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <Title level={2} className="mb-2 text-purple-600">
                  {shapeType === 'straight' ? '直线图形' : '曲线图形'}绘制
                </Title>
                <Text className="text-lg text-gray-600">
                  {shapeType === 'straight' 
                    ? '学习绘制各种直线构成的几何图形' 
                    : '练习绘制优美的曲线图形'
                  }
                </Text>
              </div>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={onBack}
                size="large"
                className="h-12 px-6"
              >
                返回
              </Button>
            </div>
          </Card>

          {/* 当前图形信息 */}
          <Card className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <Title level={4} className="text-blue-600 mb-0">
                    {currentShape.name} ({currentShapeIndex + 1}/{filteredShapes.length})
                  </Title>
                  <Tag color={getDifficultyColor(currentShape.difficulty)}>
                    {getDifficultyText(currentShape.difficulty)}
                  </Tag>
                </div>
                <Text className="text-gray-700 block mb-2">
                  {currentShape.description}
                </Text>
                {showHint && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2">
                      <Text className="text-yellow-800">
                        💡 提示：{currentShape.instruction}
                      </Text>
                    </div>
                  </motion.div>
                )}
              </div>
              <Button
                icon={<QuestionCircleOutlined />}
                onClick={toggleHint}
                type={showHint ? 'primary' : 'default'}
              >
                {showHint ? '隐藏' : '显示'}提示
              </Button>
            </div>
          </Card>

          {/* 绘画指导 */}
          <Card className="mb-6 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
            <div className="text-center">
              <Title level={4} className="text-green-600 mb-3">
                🎯 绘画要点
              </Title>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700">
                <div className="flex items-center justify-center gap-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span>{shapeType === 'straight' ? '保持线条笔直' : '保持曲线流畅'}</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  <span>注意图形的对称性</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                  <span>确保线条闭合</span>
                </div>
              </div>
            </div>
          </Card>

          {/* 绘画区域 */}
          <div className="transparent-canvas-card">
            <DrawingCanvas
              width={800}
              height={600}
              onSave={handleSave}
            />
          </div>

          {/* 完成区域 */}
          {isCompleted && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mt-6"
            >
              <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckOutlined className="text-2xl text-white" />
                  </div>
                  
                  <Title level={3} className="text-green-600 mb-3">
                    🎉 {currentShape.name}绘制完成！
                  </Title>
                  
                  <div className="mb-4">
                    <Text className="text-lg text-gray-700 block mb-2">
                      绘制评分
                    </Text>
                    <Progress 
                      percent={score} 
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }}
                      className="max-w-md mx-auto"
                    />
                  </div>

                  <Space size="large">
                    {currentShapeIndex < filteredShapes.length - 1 ? (
                      <Button
                        type="primary"
                        size="large"
                        icon={<CheckOutlined />}
                        onClick={handleNextShape}
                        className="h-12 px-8 bg-blue-500 hover:bg-blue-600"
                      >
                        下一个图形
                      </Button>
                    ) : (
                      <Button
                        type="primary"
                        size="large"
                        icon={<CheckOutlined />}
                        onClick={handleComplete}
                        className="h-12 px-8 bg-green-500 hover:bg-green-600"
                      >
                        完成练习
                      </Button>
                    )}
                    
                    <Button
                      size="large"
                      icon={<ReloadOutlined />}
                      onClick={handleRestart}
                      className="h-12 px-8"
                    >
                      重新绘制
                    </Button>
                  </Space>
                </div>
              </Card>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default ShapeDrawing
