import { Component, ErrorInfo, ReactNode } from 'react'
import { Result, Button } from 'antd'
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({ error, errorInfo })
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-2xl mx-auto p-8">
            <Result
              status="error"
              title="哎呀，出现了一些问题"
              subTitle="不用担心，这不是您的错误。我们的技术团队会尽快解决这个问题。"
              extra={[
                <Button 
                  type="primary" 
                  key="reload" 
                  icon={<ReloadOutlined />}
                  onClick={this.handleReload}
                  size="large"
                >
                  重新加载
                </Button>,
                <Button 
                  key="home" 
                  icon={<HomeOutlined />}
                  onClick={this.handleGoHome}
                  size="large"
                >
                  返回首页
                </Button>,
              ]}
            >
              <div className="text-center mt-8">
                <p className="text-gray-600 mb-4">
                  如果问题持续存在，请联系我们的客服团队：
                </p>
                <p className="text-gray-600">
                  📧 <EMAIL>
                  <br />
                  📞 400-123-4567
                </p>
                <div className="mt-6 p-4 bg-orange-50 rounded-lg">
                  <p className="text-orange-600 font-medium">
                    💝 记忆画笔团队始终为您提供温暖的技术支持
                  </p>
                </div>
              </div>
            </Result>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
