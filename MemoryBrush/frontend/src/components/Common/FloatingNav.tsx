import React, { useState } from 'react'
import { Button } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MenuOutlined,
  HomeOutlined,
  PlayCircleOutlined,
  PictureOutlined,
  TrophyOutlined,
  SettingOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  CloseOutlined,
  EditOutlined
} from '@ant-design/icons'

const FloatingNav: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [isExpanded, setIsExpanded] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(!!document.fullscreenElement)

  // 监听全屏状态变化
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  // 切换全屏
  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen()
      } else {
        await document.exitFullscreen()
      }
    } catch (error) {
      console.error('全屏切换失败:', error)
    }
  }

  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '首页',
      onClick: () => {
        navigate('/')
        setIsExpanded(false)
      }
    },
    {
      key: 'game',
      icon: <PlayCircleOutlined />,
      label: '开始绘画',
      onClick: () => {
        navigate('/game')
        setIsExpanded(false)
      }
    },
    {
      key: 'gallery',
      icon: <PictureOutlined />,
      label: '作品画廊',
      onClick: () => {
        navigate('/gallery')
        setIsExpanded(false)
      }
    },
    {
      key: 'leaderboard',
      icon: <TrophyOutlined />,
      label: '排行榜',
      onClick: () => {
        navigate('/leaderboard')
        setIsExpanded(false)
      }
    },
    {
      key: 'trace-recorder',
      icon: <EditOutlined />,
      label: '轨迹记录',
      onClick: () => {
        navigate('/trace-recorder')
        setIsExpanded(false)
      }
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => {
        navigate('/settings')
        setIsExpanded(false)
      }
    },
    {
      type: 'divider' as const
    },
    {
      key: 'fullscreen',
      icon: isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />,
      label: isFullscreen ? '退出全屏' : '进入全屏',
      onClick: () => {
        toggleFullscreen()
        setIsExpanded(false)
      }
    }
  ]

  return (
    <div className="fixed top-6 right-6 z-50">
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mb-4"
          >
            <div className="bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden">
              <div className="p-2">
                {menuItems.map((item, index) => {
                  if (item.type === 'divider') {
                    return <div key={index} className="h-px bg-gray-200 my-2" />
                  }
                  
                  const isActive =
                    (item.key === 'home' && location.pathname === '/') ||
                    (item.key === 'game' && location.pathname.startsWith('/game')) ||
                    (item.key === 'gallery' && location.pathname === '/gallery') ||
                    (item.key === 'leaderboard' && location.pathname === '/leaderboard') ||
                    (item.key === 'trace-recorder' && location.pathname === '/trace-recorder') ||
                    (item.key === 'settings' && location.pathname === '/settings')

                  return (
                    <Button
                      key={item.key}
                      type={isActive ? 'primary' : 'text'}
                      icon={item.icon}
                      onClick={item.onClick}
                      className="w-full justify-start mb-1 h-12 text-left"
                      size="large"
                    >
                      {item.label}
                    </Button>
                  )
                })}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          type="primary"
          shape="circle"
          size="large"
          icon={isExpanded ? <CloseOutlined /> : <MenuOutlined />}
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 border-0 shadow-2xl hover:shadow-3xl"
          style={{ fontSize: '18px' }}
        />
      </motion.div>
    </div>
  )
}

export default FloatingNav
