/**
 * 记忆画笔 - 类型定义
 * TypeScript类型定义文件
 */

// 基础类型
export interface BaseResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email?: string
  avatar?: string
  age?: number
  createdAt: string
  updatedAt: string
  profile?: UserProfile
}

export interface UserProfile {
  displayName: string
  age: number
  medicalCondition?: 'alzheimer' | 'parkinson' | 'healthy' | 'other'
  preferredDifficulty: 'easy' | 'medium' | 'hard'
  accessibilityNeeds: string[]
  emergencyContact?: string
}

// 游戏相关类型
export interface GameLevel {
  level: number
  name: string
  description: string
  stages: string[]
  difficulty: 'easy' | 'medium' | 'hard'
  estimatedTime: number // 分钟
  unlocked: boolean
  completed: boolean
  bestScore?: number
}

export interface GameSession {
  sessionId: string
  userId?: string
  level: number
  stage: number
  startTime: string
  endTime?: string
  status: 'active' | 'completed' | 'paused' | 'abandoned'
  currentScore: number
  timeSpent: number // 秒
}

export interface GameProgress {
  sessionId: string
  currentLevel: number
  currentStage: number
  completedStages: number
  totalStages: number
  progressPercentage: number
  timeSpent: number
  achievements: Achievement[]
}

// 绘画相关类型
export interface DrawingData {
  canvasData: string // Base64编码的画布数据
  strokes: Stroke[]
  drawingTime: number // 秒
  brushSettings: BrushSettings
  canvasSize: { width: number; height: number }
}

export interface Stroke {
  id: string
  points: Point[]
  brushSize: number
  color: string
  opacity: number
  timestamp: number
  pressure?: number // 压感（如果支持）
}

export interface Point {
  x: number
  y: number
  timestamp: number
  pressure?: number
}

export interface BrushSettings {
  size: number
  color: string
  opacity: number
  type: 'pen' | 'brush' | 'pencil' | 'marker' | 'eraser'
  texture?: string
}

// 画布相关类型
export interface CanvasConfig {
  width: number
  height: number
  backgroundColor: string
  gridEnabled: boolean
  snapToGrid: boolean
  gridSize: number
}

export interface DrawingTemplate {
  templateId: string
  level: number
  stage: number
  type: 'line_drawing' | 'shape_drawing' | 'complex_drawing' | 'photo_trace'
  instructions: string
  templateImage?: string
  guidePoints: Point[]
  expectedCompletionTime: number
  difficulty: number
}

// 认知评估类型
export interface Assessment {
  id: string
  sessionId: string
  userId?: string
  level: number
  stage: number
  score: number
  completionTime: number
  accuracy: number
  creativity: number
  motorSkills: number
  cognitiveLoad: number
  feedback: string
  suggestions: string[]
  timestamp: string
  metrics: AssessmentMetrics
}

export interface AssessmentMetrics {
  strokeCount: number
  averageStrokeLength: number
  strokeVariability: number
  pauseCount: number
  averagePauseTime: number
  handTremor: number
  coordinationScore: number
  memoryScore: number
  attentionScore: number
}

// 成就系统类型
export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'drawing' | 'progress' | 'time' | 'creativity' | 'consistency'
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  earnedAt?: string
  progress?: number
  maxProgress?: number
}

// 艺术作品类型
export interface Artwork {
  id: string
  userId?: string
  sessionId: string
  title: string
  description?: string
  imageUrl: string
  thumbnailUrl: string
  level: number
  stage: number
  createdAt: string
  isPublic: boolean
  likes: number
  tags: string[]
  metadata: ArtworkMetadata
}

export interface ArtworkMetadata {
  canvasSize: { width: number; height: number }
  strokeCount: number
  colorsUsed: string[]
  drawingTime: number
  brushTypes: string[]
  complexity: number
}

// UI状态类型
export interface UIState {
  currentTool: 'pen' | 'brush' | 'eraser' | 'select'
  brushSize: number
  brushColor: string
  brushOpacity: number
  canvasZoom: number
  showGrid: boolean
  showRuler: boolean
  isDrawing: boolean
  selectedObjects: string[]
}

// 设置类型
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  fontSize: 'small' | 'medium' | 'large' | 'extra-large'
  highContrast: boolean
  reduceMotion: boolean
  soundEnabled: boolean
  voiceGuidance: boolean
  autoSave: boolean
  autoSaveInterval: number // 秒
}

// API响应类型
export interface GameLevelResponse extends BaseResponse<GameLevel[]> {}
export interface GameSessionResponse extends BaseResponse<GameSession> {}
export interface AssessmentResponse extends BaseResponse<Assessment> {}
export interface ArtworkResponse extends BaseResponse<Artwork> {}
export interface ProgressResponse extends BaseResponse<GameProgress> {}

// 事件类型
export interface DrawingEvent {
  type: 'stroke_start' | 'stroke_move' | 'stroke_end' | 'tool_change' | 'canvas_clear'
  timestamp: number
  data: any
}

// 排行榜类型
export interface LeaderboardEntry {
  rank: number
  userName: string
  score: number
  level: number
  completionTime: number
  achievementsCount: number
  avatar?: string
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: string
}

// 类型已在上面定义并导出，无需重复导出
