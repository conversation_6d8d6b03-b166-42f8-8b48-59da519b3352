import React from 'react'
import { Routes, Route, useNavigate } from 'react-router-dom'
import { Layout, Card, Typography, Button, Space } from 'antd'
import { motion } from 'framer-motion'
import { ArrowLeftOutlined } from '@ant-design/icons'

// Hooks
import useDynamicBackground from '@hooks/useDynamicBackground'

// 页面组件
import HomePage from '@pages/HomePage'
import GamePage from '@pages/GamePage'
import ProfilePage from '@pages/ProfilePage'
import SettingsPage from '@pages/SettingsPage'
import GalleryPage from '@pages/GalleryPage'
import LeaderboardPage from '@pages/LeaderboardPage'
import TraceRecorderPage from '@pages/TraceRecorderPage'
import BackgroundTestPage from '@pages/BackgroundTestPage'

// 布局组件
// import Header from '@components/Layout/Header'
// import Footer from '@components/Layout/Footer'
// import Sidebar from '@components/Layout/Sidebar'

// 工具组件
import ErrorBoundary from '@components/Common/ErrorBoundary'
// import LoadingSpinner from '@components/Common/LoadingSpinner'
import FullscreenPrompt from '@components/Common/FullscreenPrompt'
import FloatingNav from '@components/Common/FloatingNav'

const { Content } = Layout
const { Title, Paragraph } = Typography

const App: React.FC = () => {
  // const [sidebarCollapsed, setSidebarCollapsed] = React.useState(true)
  const [showFullscreenPrompt, setShowFullscreenPrompt] = React.useState(false)
  const navigate = useNavigate()

  // 使用动态背景Hook
  useDynamicBackground()

  // 检查是否需要显示全屏提示
  React.useEffect(() => {
    const hasSeenPrompt = localStorage.getItem('memorybrush-fullscreen-prompt-seen')
    if (!hasSeenPrompt && !document.fullscreenElement) {
      // 延迟显示提示，让页面先加载完成
      const timer = setTimeout(() => {
        setShowFullscreenPrompt(true)
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [])

  // 进入全屏
  const handleEnterFullscreen = async () => {
    try {
      await document.documentElement.requestFullscreen()
      localStorage.setItem('memorybrush-fullscreen-prompt-seen', 'true')
    } catch (error) {
      console.error('进入全屏失败:', error)
    }
  }

  // 关闭全屏提示
  const handleCloseFullscreenPrompt = () => {
    setShowFullscreenPrompt(false)
    localStorage.setItem('memorybrush-fullscreen-prompt-seen', 'true')
  }

  return (
    <ErrorBoundary>
      <Layout className="min-h-screen" style={{ background: 'transparent' }}>
        {/* 顶部导航 - 已隐藏 */}
        {/* <Header
          collapsed={sidebarCollapsed}
          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        /> */}

        <Layout>
          {/* 侧边栏 - 已隐藏 */}
          {/* <Sidebar collapsed={sidebarCollapsed} /> */}

          {/* 主内容区域 */}
          <Layout className="transition-all duration-300">
            <Content className="min-h-screen">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Routes>
                  {/* 首页 */}
                  <Route path="/" element={<HomePage />} />

                  {/* 游戏页面 */}
                  <Route path="/game" element={<GamePage />} />
                  <Route path="/game/:level" element={<GamePage />} />
                  <Route path="/game/:level/:stage" element={<GamePage />} />

                  {/* 个人资料 */}
                  <Route path="/profile" element={<ProfilePage />} />

                  {/* 设置页面 */}
                  <Route path="/settings" element={<SettingsPage />} />

                  {/* 作品画廊 */}
                  <Route path="/gallery" element={<GalleryPage />} />

                  {/* 排行榜 */}
                  <Route path="/leaderboard" element={<LeaderboardPage />} />

                  {/* 轨迹记录工具 */}
                  <Route path="/trace-recorder" element={<TraceRecorderPage />} />

                  {/* 背景测试页面 */}
                  <Route path="/background-test" element={<BackgroundTestPage />} />

                  {/* 404 页面 */}
                  <Route path="*" element={
                    <div className="min-h-screen pt-8" style={{ background: 'transparent' }}>
                      <div className="max-w-4xl mx-auto px-4 py-8">
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.6 }}
                        >
                          <Card className="text-center p-12 shadow-soft">
                            <div className="mb-8">
                              <div className="w-24 h-24 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center mx-auto mb-6">
                                <span className="text-4xl text-white">🔍</span>
                              </div>

                              <Title level={1} className="text-gray-600 mb-4">
                                页面建设中
                              </Title>

                              <Paragraph className="text-xl text-gray-600 max-w-2xl mx-auto">
                                您访问的页面正在建设中，敬请期待！
                                <br />
                                我们正在努力为您提供更好的功能体验。
                              </Paragraph>
                            </div>

                            <Space size="large">
                              <Button
                                size="large"
                                icon={<ArrowLeftOutlined />}
                                onClick={() => navigate('/')}
                                className="h-12 px-8 text-lg"
                              >
                                返回首页
                              </Button>
                            </Space>
                          </Card>
                        </motion.div>
                      </div>
                    </div>
                  } />
                </Routes>
              </motion.div>
            </Content>

            {/* 底部 - 已隐藏 */}
            {/* <Footer /> */}
          </Layout>
        </Layout>
      </Layout>

      {/* 浮动导航 */}
      <FloatingNav />

      {/* 全屏提示 */}
      <FullscreenPrompt
        visible={showFullscreenPrompt}
        onClose={handleCloseFullscreenPrompt}
        onEnterFullscreen={handleEnterFullscreen}
      />
    </ErrorBoundary>
  )
}

export default App
