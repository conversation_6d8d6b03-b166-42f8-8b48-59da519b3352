var Pr=Object.defineProperty;var Rr=(e,t,n)=>t in e?Pr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var _t=(e,t,n)=>Rr(e,typeof t!="symbol"?t+"":t,n);import{r as u,c as _r,b as Ir,g as Lr,R as Ze}from"./vendor-BTWMFwqw.js";import{T as Le,C as ce,R as We,a as ct,b as Bt,P as Nn,c as Ls,d as gt,e as En,f as qt,g as As,h as Ar,i as $r,j as Or,k as bt,B as q,l as kn,m as dt,n as Ve,s as Y,S as fe,o as Cn,p as Dr,q as Tn,r as Pn,t as yt,u as Bs,I as qs,v as Qe,w as Fr,x as Gt,y as Hs,z as ws,A as Rn,D as Mr,E as $s,F as _n,M as Ht,G as Ur,H as zr,J as Ws,K as Br,L as It,N as qr,O as Hr,Q as Wr,U as In,V as Ss,W as Ln,X as Jr,Y as Gr,Z as Vr,_ as Ft,$ as Yr}from"./antd-SiMkeERs.js";import{m as re,A as Xr}from"./animation-BAuEqiwG.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(a){if(a.ep)return;a.ep=!0;const o=n(a);fetch(a.href,o)}})();var An={exports:{}},Vt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kr=u,Qr=Symbol.for("react.element"),Zr=Symbol.for("react.fragment"),ea=Object.prototype.hasOwnProperty,ta=Kr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,sa={key:!0,ref:!0,__self:!0,__source:!0};function $n(e,t,n){var r,a={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)ea.call(t,r)&&!sa.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)a[r]===void 0&&(a[r]=t[r]);return{$$typeof:Qr,type:e,key:o,ref:i,props:a,_owner:ta.current}}Vt.Fragment=Zr;Vt.jsx=$n;Vt.jsxs=$n;An.exports=Vt;var s=An.exports,Ns={},Js=_r;Ns.createRoot=Js.createRoot,Ns.hydrateRoot=Js.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function jt(){return jt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jt.apply(this,arguments)}var et;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(et||(et={}));const Gs="popstate";function na(e){e===void 0&&(e={});function t(r,a){let{pathname:o,search:i,hash:c}=r.location;return Es("",{pathname:o,search:i,hash:c},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:Dn(a)}return aa(t,n,null,e)}function we(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function On(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ra(){return Math.random().toString(36).substr(2,8)}function Vs(e,t){return{usr:e.state,key:e.key,idx:t}}function Es(e,t,n,r){return n===void 0&&(n=null),jt({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?ut(t):t,{state:n,key:t&&t.key||r||ra()})}function Dn(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function ut(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function aa(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,c=et.Pop,l=null,h=d();h==null&&(h=0,i.replaceState(jt({},i.state,{idx:h}),""));function d(){return(i.state||{idx:null}).idx}function m(){c=et.Pop;let x=d(),T=x==null?null:x-h;h=x,l&&l({action:c,location:j.location,delta:T})}function v(x,T){c=et.Push;let C=Es(j.location,x,T);h=d()+1;let E=Vs(C,h),z=j.createHref(C);try{i.pushState(E,"",z)}catch(K){if(K instanceof DOMException&&K.name==="DataCloneError")throw K;a.location.assign(z)}o&&l&&l({action:c,location:j.location,delta:1})}function R(x,T){c=et.Replace;let C=Es(j.location,x,T);h=d();let E=Vs(C,h),z=j.createHref(C);i.replaceState(E,"",z),o&&l&&l({action:c,location:j.location,delta:0})}function b(x){let T=a.location.origin!=="null"?a.location.origin:a.location.href,C=typeof x=="string"?x:Dn(x);return C=C.replace(/ $/,"%20"),we(T,"No window.location.(origin|href) available to create URL for href: "+C),new URL(C,T)}let j={get action(){return c},get location(){return e(a,i)},listen(x){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(Gs,m),l=x,()=>{a.removeEventListener(Gs,m),l=null}},createHref(x){return t(a,x)},createURL:b,encodeLocation(x){let T=b(x);return{pathname:T.pathname,search:T.search,hash:T.hash}},push:v,replace:R,go(x){return i.go(x)}};return j}var Ys;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Ys||(Ys={}));function oa(e,t,n){return n===void 0&&(n="/"),ia(e,t,n)}function ia(e,t,n,r){let a=typeof t=="string"?ut(t):t,o=Un(a.pathname||"/",n);if(o==null)return null;let i=Fn(e);la(i);let c=null;for(let l=0;c==null&&l<i.length;++l){let h=ja(o);c=ga(i[l],h)}return c}function Fn(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(o,i,c)=>{let l={relativePath:c===void 0?o.path||"":c,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};l.relativePath.startsWith("/")&&(we(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let h=st([r,l.relativePath]),d=n.concat(l);o.children&&o.children.length>0&&(we(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+h+'".')),Fn(o.children,t,d,h)),!(o.path==null&&!o.index)&&t.push({path:h,score:pa(h,o.index),routesMeta:d})};return e.forEach((o,i)=>{var c;if(o.path===""||!((c=o.path)!=null&&c.includes("?")))a(o,i);else for(let l of Mn(o.path))a(o,i,l)}),t}function Mn(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return a?[o,""]:[o];let i=Mn(r.join("/")),c=[];return c.push(...i.map(l=>l===""?o:[o,l].join("/"))),a&&c.push(...i),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function la(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:xa(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ca=/^:[\w-]+$/,da=3,ua=2,ha=1,ma=10,fa=-2,Xs=e=>e==="*";function pa(e,t){let n=e.split("/"),r=n.length;return n.some(Xs)&&(r+=fa),t&&(r+=ua),n.filter(a=>!Xs(a)).reduce((a,o)=>a+(ca.test(o)?da:o===""?ha:ma),r)}function xa(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function ga(e,t,n){let{routesMeta:r}=e,a={},o="/",i=[];for(let c=0;c<r.length;++c){let l=r[c],h=c===r.length-1,d=o==="/"?t:t.slice(o.length)||"/",m=ya({path:l.relativePath,caseSensitive:l.caseSensitive,end:h},d),v=l.route;if(!m)return null;Object.assign(a,m.params),i.push({params:a,pathname:st([o,m.pathname]),pathnameBase:ka(st([o,m.pathnameBase])),route:v}),m.pathnameBase!=="/"&&(o=st([o,m.pathnameBase]))}return i}function ya(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=ba(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),c=a.slice(1);return{params:r.reduce((h,d,m)=>{let{paramName:v,isOptional:R}=d;if(v==="*"){let j=c[m]||"";i=o.slice(0,o.length-j.length).replace(/(.)\/+$/,"$1")}const b=c[m];return R&&!b?h[v]=void 0:h[v]=(b||"").replace(/%2F/g,"/"),h},{}),pathname:o,pathnameBase:i,pattern:e}}function ba(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),On(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,c,l)=>(r.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function ja(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return On(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Un(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function va(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?ut(e):e;return{pathname:n?n.startsWith("/")?n:wa(n,t):t,search:Ca(r),hash:Ta(a)}}function wa(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function ps(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Sa(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Na(e,t){let n=Sa(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Ea(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=ut(e):(a=jt({},e),we(!a.pathname||!a.pathname.includes("?"),ps("?","pathname","search",a)),we(!a.pathname||!a.pathname.includes("#"),ps("#","pathname","hash",a)),we(!a.search||!a.search.includes("#"),ps("#","search","hash",a)));let o=e===""||a.pathname==="",i=o?"/":a.pathname,c;if(i==null)c=n;else{let m=t.length-1;if(!r&&i.startsWith("..")){let v=i.split("/");for(;v[0]==="..";)v.shift(),m-=1;a.pathname=v.join("/")}c=m>=0?t[m]:"/"}let l=va(a,c),h=i&&i!=="/"&&i.endsWith("/"),d=(o||i===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(h||d)&&(l.pathname+="/"),l}const st=e=>e.join("/").replace(/\/\/+/g,"/"),ka=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ca=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ta=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Pa(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const zn=["post","put","patch","delete"];new Set(zn);const Ra=["get",...zn];new Set(Ra);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function vt(){return vt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vt.apply(this,arguments)}const Os=u.createContext(null),_a=u.createContext(null),Yt=u.createContext(null),Xt=u.createContext(null),at=u.createContext({outlet:null,matches:[],isDataRoute:!1}),Bn=u.createContext(null);function Kt(){return u.useContext(Xt)!=null}function Qt(){return Kt()||we(!1),u.useContext(Xt).location}function qn(e){u.useContext(Yt).static||u.useLayoutEffect(e)}function Ye(){let{isDataRoute:e}=u.useContext(at);return e?Wa():Ia()}function Ia(){Kt()||we(!1);let e=u.useContext(Os),{basename:t,future:n,navigator:r}=u.useContext(Yt),{matches:a}=u.useContext(at),{pathname:o}=Qt(),i=JSON.stringify(Na(a,n.v7_relativeSplatPath)),c=u.useRef(!1);return qn(()=>{c.current=!0}),u.useCallback(function(h,d){if(d===void 0&&(d={}),!c.current)return;if(typeof h=="number"){r.go(h);return}let m=Ea(h,JSON.parse(i),o,d.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:st([t,m.pathname])),(d.replace?r.replace:r.push)(m,d.state,d)},[t,r,i,o,e])}function La(){let{matches:e}=u.useContext(at),t=e[e.length-1];return t?t.params:{}}function Aa(e,t){return $a(e,t)}function $a(e,t,n,r){Kt()||we(!1);let{navigator:a}=u.useContext(Yt),{matches:o}=u.useContext(at),i=o[o.length-1],c=i?i.params:{};i&&i.pathname;let l=i?i.pathnameBase:"/";i&&i.route;let h=Qt(),d;if(t){var m;let x=typeof t=="string"?ut(t):t;l==="/"||(m=x.pathname)!=null&&m.startsWith(l)||we(!1),d=x}else d=h;let v=d.pathname||"/",R=v;if(l!=="/"){let x=l.replace(/^\//,"").split("/");R="/"+v.replace(/^\//,"").split("/").slice(x.length).join("/")}let b=oa(e,{pathname:R}),j=Ua(b&&b.map(x=>Object.assign({},x,{params:Object.assign({},c,x.params),pathname:st([l,a.encodeLocation?a.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?l:st([l,a.encodeLocation?a.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),o,n,r);return t&&j?u.createElement(Xt.Provider,{value:{location:vt({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:et.Pop}},j):j}function Oa(){let e=Ha(),t=Pa(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),n?u.createElement("pre",{style:a},n):null,null)}const Da=u.createElement(Oa,null);class Fa extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?u.createElement(at.Provider,{value:this.props.routeContext},u.createElement(Bn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ma(e){let{routeContext:t,match:n,children:r}=e,a=u.useContext(Os);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),u.createElement(at.Provider,{value:t},r)}function Ua(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,c=(a=n)==null?void 0:a.errors;if(c!=null){let d=i.findIndex(m=>m.route.id&&(c==null?void 0:c[m.route.id])!==void 0);d>=0||we(!1),i=i.slice(0,Math.min(i.length,d+1))}let l=!1,h=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<i.length;d++){let m=i[d];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=d),m.route.id){let{loaderData:v,errors:R}=n,b=m.route.loader&&v[m.route.id]===void 0&&(!R||R[m.route.id]===void 0);if(m.route.lazy||b){l=!0,h>=0?i=i.slice(0,h+1):i=[i[0]];break}}}return i.reduceRight((d,m,v)=>{let R,b=!1,j=null,x=null;n&&(R=c&&m.route.id?c[m.route.id]:void 0,j=m.route.errorElement||Da,l&&(h<0&&v===0?(Ja("route-fallback"),b=!0,x=null):h===v&&(b=!0,x=m.route.hydrateFallbackElement||null)));let T=t.concat(i.slice(0,v+1)),C=()=>{let E;return R?E=j:b?E=x:m.route.Component?E=u.createElement(m.route.Component,null):m.route.element?E=m.route.element:E=d,u.createElement(Ma,{match:m,routeContext:{outlet:d,matches:T,isDataRoute:n!=null},children:E})};return n&&(m.route.ErrorBoundary||m.route.errorElement||v===0)?u.createElement(Fa,{location:n.location,revalidation:n.revalidation,component:j,error:R,children:C(),routeContext:{outlet:null,matches:T,isDataRoute:!0}}):C()},null)}var Hn=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Hn||{}),Wn=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Wn||{});function za(e){let t=u.useContext(Os);return t||we(!1),t}function Ba(e){let t=u.useContext(_a);return t||we(!1),t}function qa(e){let t=u.useContext(at);return t||we(!1),t}function Jn(e){let t=qa(),n=t.matches[t.matches.length-1];return n.route.id||we(!1),n.route.id}function Ha(){var e;let t=u.useContext(Bn),n=Ba(),r=Jn();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Wa(){let{router:e}=za(Hn.UseNavigateStable),t=Jn(Wn.UseNavigateStable),n=u.useRef(!1);return qn(()=>{n.current=!0}),u.useCallback(function(a,o){o===void 0&&(o={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,vt({fromRouteId:t},o)))},[e,t])}const Ks={};function Ja(e,t,n){Ks[e]||(Ks[e]=!0)}function Ga(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function ze(e){we(!1)}function Va(e){let{basename:t="/",children:n=null,location:r,navigationType:a=et.Pop,navigator:o,static:i=!1,future:c}=e;Kt()&&we(!1);let l=t.replace(/^\/*/,"/"),h=u.useMemo(()=>({basename:l,navigator:o,static:i,future:vt({v7_relativeSplatPath:!1},c)}),[l,c,o,i]);typeof r=="string"&&(r=ut(r));let{pathname:d="/",search:m="",hash:v="",state:R=null,key:b="default"}=r,j=u.useMemo(()=>{let x=Un(d,l);return x==null?null:{location:{pathname:x,search:m,hash:v,state:R,key:b},navigationType:a}},[l,d,m,v,R,b,a]);return j==null?null:u.createElement(Yt.Provider,{value:h},u.createElement(Xt.Provider,{children:n,value:j}))}function Ya(e){let{children:t,location:n}=e;return Aa(ks(t),n)}new Promise(()=>{});function ks(e,t){t===void 0&&(t=[]);let n=[];return u.Children.forEach(e,(r,a)=>{if(!u.isValidElement(r))return;let o=[...t,a];if(r.type===u.Fragment){n.push.apply(n,ks(r.props.children,o));return}r.type!==ze&&we(!1),!r.props.index||!r.props.children||we(!1);let i={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=ks(r.props.children,o)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Xa="6";try{window.__reactRouterVersion=Xa}catch{}const Ka="startTransition",Qs=Ir[Ka];function Qa(e){let{basename:t,children:n,future:r,window:a}=e,o=u.useRef();o.current==null&&(o.current=na({window:a,v5Compat:!0}));let i=o.current,[c,l]=u.useState({action:i.action,location:i.location}),{v7_startTransition:h}=r||{},d=u.useCallback(m=>{h&&Qs?Qs(()=>l(m)):l(m)},[l,h]);return u.useLayoutEffect(()=>i.listen(d),[i,d]),u.useEffect(()=>Ga(r),[r]),u.createElement(Va,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:i,future:r})}var Zs;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Zs||(Zs={}));var en;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(en||(en={}));var Zt={},Gn={exports:{}};(function(e){function t(n){return n&&n.__esModule?n:{default:n}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Gn);var es=Gn.exports,ts={};Object.defineProperty(ts,"__esModule",{value:!0});ts.default=void 0;var Za={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};ts.default=Za;var ss={},St={},ns={},Vn={exports:{}},Yn={exports:{}},Xn={exports:{}},Kn={exports:{}};(function(e){function t(n){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Kn);var Qn=Kn.exports,Zn={exports:{}};(function(e){var t=Qn.default;function n(r,a){if(t(r)!="object"||!r)return r;var o=r[Symbol.toPrimitive];if(o!==void 0){var i=o.call(r,a||"default");if(t(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Zn);var eo=Zn.exports;(function(e){var t=Qn.default,n=eo;function r(a){var o=n(a,"string");return t(o)=="symbol"?o:o+""}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Xn);var to=Xn.exports;(function(e){var t=to;function n(r,a,o){return(a=t(a))in r?Object.defineProperty(r,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[a]=o,r}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Yn);var so=Yn.exports;(function(e){var t=so;function n(a,o){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(a);o&&(c=c.filter(function(l){return Object.getOwnPropertyDescriptor(a,l).enumerable})),i.push.apply(i,c)}return i}function r(a){for(var o=1;o<arguments.length;o++){var i=arguments[o]!=null?arguments[o]:{};o%2?n(Object(i),!0).forEach(function(c){t(a,c,i[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach(function(c){Object.defineProperty(a,c,Object.getOwnPropertyDescriptor(i,c))})}return a}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Vn);var no=Vn.exports,rs={};Object.defineProperty(rs,"__esModule",{value:!0});rs.commonLocale=void 0;rs.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0};var ro=es.default;Object.defineProperty(ns,"__esModule",{value:!0});ns.default=void 0;var tn=ro(no),ao=rs,oo=(0,tn.default)((0,tn.default)({},ao.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});ns.default=oo;var Nt={};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.default=void 0;const io={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};Nt.default=io;var er=es.default;Object.defineProperty(St,"__esModule",{value:!0});St.default=void 0;var lo=er(ns),co=er(Nt);const tr={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},lo.default),timePickerLocale:Object.assign({},co.default)};tr.lang.ok="确定";St.default=tr;var uo=es.default;Object.defineProperty(ss,"__esModule",{value:!0});ss.default=void 0;var ho=uo(St);ss.default=ho.default;var as=es.default;Object.defineProperty(Zt,"__esModule",{value:!0});Zt.default=void 0;var mo=as(ts),fo=as(ss),po=as(St),xo=as(Nt);const De="${label}不是一个有效的${type}",go={locale:"zh-cn",Pagination:mo.default,DatePicker:po.default,TimePicker:xo.default,Calendar:fo.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:De,method:De,array:De,object:De,number:De,date:De,boolean:De,integer:De,float:De,regexp:De,email:De,url:De,hex:De},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};Zt.default=go;var yo=Zt;const bo=Lr(yo),jo=()=>{const e=Qt();u.useEffect(()=>{const t=e.pathname.split("/").filter(o=>o.length>0);let n="";if(t[0]==="game"&&t[1]==="level-1"&&t.length===2){document.body.style.backgroundImage="",document.documentElement.style.backgroundImage="";return}if(t[0]==="game"&&t.length>=3){const o=t[1],i=t[2];if(i.startsWith("L")&&i.includes("-"))n=i;else if(o.startsWith("level-")&&i.startsWith("stage-")){const c=o.replace("level-",""),l=i.replace("stage-","");n=`L${c}-${l}`}else o.startsWith("L")&&!o.includes("-")?n=`${o}-${i}`:o.startsWith("level-")&&i.startsWith("L")&&(n=i)}const r=o=>{document.body.style.backgroundImage=`url('${o}')`,document.documentElement.style.backgroundImage=`url('${o}')`};return n?(async o=>{if(o.startsWith("L1-")){const i=["jpg","jpeg","png"];for(const c of i)try{const l=`/${o}/background.${c}`,h=new Image;await new Promise((d,m)=>{h.onload=d,h.onerror=m,h.src=l}),r(l),console.log("✅ 成功加载L1关卡背景:",l);return}catch{continue}r("/background.jpg"),console.log("⚠️ L1关卡背景不存在，使用统一背景: /background.jpg")}else r("/background.jpg"),console.log("✅ 使用统一背景:","/background.jpg")})(n):(r("/background.jpg"),console.log("✅ 非游戏页面使用统一背景:","/background.jpg")),()=>{}},[e.pathname])},{Title:Lt,Text:Ne}=Le,vo=()=>{const e=Ye(),t={currentLevel:2,completedStages:5,overallProgress:42,consecutiveDays:5};return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-6xl mx-auto px-4 pt-8 pb-12",children:[s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ce,{className:"bg-gradient-to-br from-orange-400 to-pink-500 border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/game"),children:s.jsxs("div",{className:"text-center text-white h-full flex flex-col justify-center",children:[s.jsx(We,{className:"text-6xl mb-4"}),s.jsx(Lt,{level:2,className:"text-white mb-2",children:"开始绘画"}),s.jsx(Ne,{className:"text-white text-xl opacity-90",children:"让创意自由流淌"})]})})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ce,{className:"bg-white border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/gallery"),children:s.jsxs("div",{className:"text-center h-full flex flex-col justify-center",children:[s.jsx(ct,{className:"text-6xl text-purple-500 mb-4"}),s.jsx(Lt,{level:2,className:"text-gray-700 mb-2",children:"作品画廊"}),s.jsx(Ne,{className:"text-gray-600 text-xl",children:"欣赏美好作品"})]})})})]})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mb-16",children:s.jsx(ce,{className:"bg-white shadow-soft border-0 max-w-4xl mx-auto",children:s.jsxs("div",{className:"text-center p-8",children:[s.jsxs(Lt,{level:2,className:"text-gray-700 mb-8",children:[s.jsx(Bt,{className:"mr-3 text-yellow-500"}),"您的学习进度"]}),s.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 mb-8",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(Ne,{className:"text-2xl text-gray-700",children:"艺术之旅进度"}),s.jsxs(Ne,{className:"text-4xl font-bold text-purple-600",children:[t.overallProgress,"%"]})]}),s.jsx(Nn,{percent:t.overallProgress,strokeColor:{"0%":"#3b82f6","50%":"#8b5cf6","100%":"#ec4899"},strokeWidth:20,className:"mb-4"}),s.jsx(Ne,{className:"text-xl text-gray-600",children:"每一步都是成长，每一画都是进步 ✨"})]}),s.jsxs(Ls,{gutter:24,children:[s.jsx(gt,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🎨"}),s.jsx(Ne,{className:"text-4xl font-bold text-orange-600 block",children:t.currentLevel}),s.jsx(Ne,{className:"text-xl text-gray-600",children:"当前级别"})]})}),s.jsx(gt,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-green-100 to-green-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"⭐"}),s.jsx(Ne,{className:"text-4xl font-bold text-green-600 block",children:t.completedStages}),s.jsx(Ne,{className:"text-xl text-gray-600",children:"完成阶段"})]})}),s.jsx(gt,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-pink-100 to-pink-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🔥"}),s.jsx(Ne,{className:"text-4xl font-bold text-pink-600 block",children:t.consecutiveDays}),s.jsx(Ne,{className:"text-xl text-gray-600",children:"连续天数"})]})})]})]})})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto",children:[s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/profile"),children:[s.jsx(En,{className:"text-4xl text-blue-500 mb-2"}),s.jsx(Ne,{className:"text-lg font-medium text-gray-700",children:"个人档案"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-yellow-50 to-yellow-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/leaderboard"),children:[s.jsx(Bt,{className:"text-4xl text-yellow-500 mb-2"}),s.jsx(Ne,{className:"text-lg font-medium text-gray-700",children:"排行榜"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/achievements"),children:[s.jsx(qt,{className:"text-4xl text-purple-500 mb-2"}),s.jsx(Ne,{className:"text-lg font-medium text-gray-700",children:"成就"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/settings"),children:[s.jsx(As,{className:"text-4xl text-green-500 mb-2"}),s.jsx(Ne,{className:"text-lg font-medium text-gray-700",children:"设置"})]})})]})}),s.jsx(re.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1.2},className:"text-center mt-16 py-12",children:s.jsx("div",{className:"max-w-3xl mx-auto",children:s.jsxs("div",{className:"bg-gradient-to-r from-orange-100 via-pink-100 to-purple-100 rounded-2xl p-8 shadow-soft",children:[s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(Ar,{className:"text-2xl text-white"})})}),s.jsx(Lt,{level:2,className:"text-3xl mb-4",children:s.jsx("span",{className:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"让艺术点亮记忆，让创造温暖心灵"})}),s.jsx(Ne,{className:"text-xl text-gray-700 mb-6",children:"每一笔都是希望，每一画都是奇迹"}),s.jsx(Ne,{className:"text-lg text-gray-600",children:"奇迹创造者团队 · 用心陪伴您的艺术之旅 ✨"})]})})})]})})},os=[{id:"level-1",title:"线条启蒙",description:"从基础的线条开始，培养手部协调能力",icon:"📈",difficulty:1,stages:[{id:"L1-1",title:"自由画线",description:"随意画线，创作属于您的艺术作品",type:"free-draw"},{id:"L1-2",title:"匀速直线",description:"跟随引导线条，提高绘画精准度",type:"image-trace"},{id:"L1-3",title:"匀速线条组合",description:"从真实图片中抽取线条进行描画练习",type:"image-trace"},{id:"L1-4",title:"曲线消除",description:"绘制曲线和复杂图形",type:"image-trace"},{id:"L1-5",title:"直线图形",description:"绘制三角形、正方形、矩形等直线图形",type:"shape-straight"}]},{id:"level-2",title:"立体空间",description:"从二维图形到三维立体，提升空间想象力",icon:"📦",difficulty:2,stages:[{id:"L2-1",title:"立体图形",description:"绘制锥形、立方体、圆柱体等三维图形",type:"coming-soon",comingSoon:!0},{id:"L2-2",title:"色彩填充",description:"为几何图形填色，学习色系和色谱搭配",type:"coming-soon",comingSoon:!0},{id:"L2-3",title:"质感画笔",description:"复杂曲线描边，选择不同质感的画笔填色",type:"coming-soon",comingSoon:!0},{id:"L2-4",title:"阴影效果",description:"学习光影关系，添加阴影效果",type:"coming-soon",comingSoon:!0}]},{id:"level-3",title:"画面构图",description:"通过引导线条，完成完整的艺术画面",icon:"🖼️",difficulty:3,stages:[{id:"L3-1",title:"抽象艺术",description:"用抽象线条和色块创作现代艺术作品",type:"coming-soon",comingSoon:!0},{id:"L3-2",title:"几何静物",description:"绘制几何形状组成的静物画",type:"coming-soon",comingSoon:!0},{id:"L3-3",title:"风景艺术",description:"创作简单的风景画作品",type:"coming-soon",comingSoon:!0},{id:"L3-4",title:"肖像艺术",description:"学习人物肖像的基本绘制",type:"coming-soon",comingSoon:!0}]},{id:"level-4",title:"智能创作",description:"上传照片，AI辅助创作个性化艺术作品",icon:"🤖",difficulty:4,stages:[{id:"L4-1",title:"照片描边",description:"上传照片，提取轮廓进行描边练习",type:"coming-soon",comingSoon:!0},{id:"L4-2",title:"风格渲染",description:"选择不同艺术风格，AI辅助渲染作品",type:"coming-soon",comingSoon:!0},{id:"L4-3",title:"AI协作",description:"与AI协作，创作独特的个人艺术作品",type:"coming-soon",comingSoon:!0},{id:"L4-4",title:"创意模式",description:"自由创作模式，发挥无限想象力",type:"coming-soon",comingSoon:!0}]}],wo=()=>os.flatMap(e=>e.stages.map(t=>t.id)),So=()=>os.map(e=>e.id),{Title:sn,Text:nn}=Le,No=({onSelectLevel:e,unlockedLevels:t=["level-1"],onResetProgress:n})=>{const r=os.map(i=>({...i,icon:i.id==="level-1"?s.jsx($r,{className:"text-4xl"}):i.id==="level-2"?s.jsx(Or,{className:"text-4xl"}):i.id==="level-3"?s.jsx(ct,{className:"text-4xl"}):i.id==="level-4"?s.jsx(qt,{className:"text-4xl"}):s.jsx(We,{className:"text-4xl"})})),a=i=>{switch(i){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=i=>{switch(i){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}};return s.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-8",children:[s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-8",children:[s.jsx(sn,{level:1,className:"text-purple-600 mb-4",children:"选择游戏级别"}),s.jsx(nn,{className:"text-xl text-gray-600",children:"从简单的线条开始，逐步提升您的绘画技能"})]}),s.jsx(Ls,{gutter:[24,24],children:r.map((i,c)=>{const l=t.includes(i.id);return s.jsx(gt,{xs:24,lg:12,children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:c*.1},children:s.jsxs(ce,{className:`h-full shadow-lg transition-all duration-300 ${l?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsxs("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white relative ${l?"bg-gradient-to-br from-purple-400 to-purple-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:[!l&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx(bt,{className:"text-2xl"})}),l&&i.icon]}),s.jsx(sn,{level:3,className:`mb-2 ${l?"":"text-gray-500"}`,children:i.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-3 ${l?a(i.difficulty):"bg-gray-100 text-gray-500"}`,children:[s.jsx(qt,{className:"mr-1"}),o(i.difficulty)]}),s.jsx(nn,{className:`block ${l?"text-gray-600":"text-gray-500"}`,children:l?i.description:"完成前面的级别来解锁"})]}),s.jsx("div",{className:"mt-6",children:s.jsx(re.div,{whileHover:l?{scale:1.02}:{},whileTap:l?{scale:.98}:{},children:s.jsx(q,{type:l?"primary":"default",size:"large",icon:l?s.jsx(We,{}):s.jsx(bt,{}),onClick:()=>l&&e(i.id),disabled:!l,className:"w-full h-12 text-lg",children:l?"进入级别":"级别锁定"})})})]})})},i.id)})}),n&&s.jsx("div",{className:"text-center mt-8",children:s.jsx(kn,{title:"重置游戏进度",description:"确定要重置所有游戏进度吗？这将清除所有解锁的级别。",onConfirm:n,okText:"确定",cancelText:"取消",children:s.jsx(q,{icon:s.jsx(dt,{}),size:"small",className:"text-gray-500 hover:text-gray-700",children:"重置进度"})})})]})},Ds={get allLevels(){return So()},get allStages(){return wo()}},Eo=()=>Ds.allLevels,ko=()=>Ds.allStages,sr=e=>Ds.allStages.includes(e),{Title:xs,Text:rn}=Le,Co=({level:e,onSelectStage:t,onBack:n,unlockedStages:r=[]})=>{if(e.id==="level-1")return s.jsx(To,{level:e,onSelectStage:t,onBack:n,unlockedStages:r});const a=c=>{switch(c){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=c=>{switch(c){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}},i=(c,l)=>sr(c);return s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl",children:(typeof e.icon=="string",e.icon)}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx(xs,{level:2,className:"mb-0 text-purple-600",children:e.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a(e.difficulty)}`,children:[s.jsx(qt,{className:"mr-1"}),o(e.difficulty)]})]}),s.jsx(rn,{className:"text-lg text-gray-600",children:e.description})]})]}),s.jsx(q,{icon:s.jsx(Ve,{}),onClick:n,size:"large",className:"h-12 px-6",children:"返回级别选择"})]})}),s.jsx(Ls,{gutter:[24,24],children:e.stages.map((c,l)=>{const h=i(c.id),d=!c.comingSoon&&h;return s.jsx(gt,{xs:24,sm:12,lg:8,children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:l*.1},whileHover:d?{scale:1.02}:{},whileTap:d?{scale:.98}:{},children:s.jsx(ce,{className:`h-full shadow-lg transition-all duration-300 ${d?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},onClick:()=>d&&t(c.id),children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white ${d?"bg-gradient-to-br from-blue-400 to-blue-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:d?s.jsx(We,{className:"text-2xl"}):s.jsx(bt,{className:"text-2xl"})}),s.jsxs(xs,{level:4,className:`mb-3 ${d?"":"text-gray-500"}`,children:[c.title,c.comingSoon&&s.jsx("div",{className:"mt-2",children:s.jsx("span",{className:"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full",children:"即将推出"})})]}),s.jsx(rn,{className:`block mb-6 ${d?"text-gray-600":"text-gray-500"}`,children:c.description}),s.jsx(q,{type:d?"primary":"default",size:"large",icon:d?s.jsx(We,{}):s.jsx(bt,{}),disabled:!d,className:"w-full h-12",children:d?"开始练习":c.comingSoon?"即将推出":"完成前面练习解锁"})]})})})},c.id)})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"mt-8",children:s.jsx(ce,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx(xs,{level:4,className:"text-blue-600 mb-3",children:"💡 学习建议"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"按顺序完成练习效果更佳"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"每次练习时间不宜过长"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-pink-400 rounded-full"}),s.jsx("span",{children:"重复练习有助于提高技能"})]})]})]})})})]})})},To=({level:e,onSelectStage:t,onBack:n,unlockedStages:r=[]})=>{const a=window.location.search.includes("debug=true"),o=(l,h)=>sr(l),i=[{id:"L1-1",x:"29%",y:"46%",number:1},{id:"L1-2",x:"35%",y:"28%",number:2},{id:"L1-3",x:"48%",y:"19%",number:3},{id:"L1-4",x:"61%",y:"29%",number:4},{id:"L1-5",x:"67%",y:"47%",number:5}],c=l=>{e.stages.findIndex(d=>d.id===l),o(l)&&t(l)};return s.jsx("div",{className:"min-h-screen relative overflow-hidden",children:s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",style:{backgroundColor:"#f0f0f0"},children:s.jsxs("div",{className:"relative",children:[s.jsx("img",{src:"/background-l1.jpg",alt:"Level 1 Background",className:"h-screen w-auto object-contain",style:{maxWidth:"none"}}),s.jsxs("div",{className:"absolute inset-0",children:[s.jsx("div",{className:"absolute top-8 left-8 z-10",children:s.jsx(q,{icon:s.jsx(Ve,{}),onClick:n,size:"large",className:"h-12 px-6 bg-white/90 backdrop-blur-sm hover:bg-white",children:"返回级别选择"})}),i.map((l,h)=>{const d=e.stages.find(v=>v.id===l.id),m=o(l.id);return d?s.jsxs(re.div,{className:"absolute z-20",style:{left:l.x,top:l.y,transform:"translate(-50%, -50%)"},initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{duration:.6,delay:h*.1},whileHover:m?{scale:1.1}:{},whileTap:m?{scale:.95}:{},children:[s.jsx("div",{className:`
                                            w-20 h-20 rounded-full flex items-center justify-center text-white text-xl font-bold
                                            cursor-pointer transition-all duration-300 shadow-lg
                                            ${m?"bg-gradient-to-br from-yellow-400 to-yellow-600 hover:shadow-xl":"bg-gradient-to-br from-gray-400 to-gray-500 cursor-not-allowed opacity-60"}
                                            ${a?"ring-2 ring-red-500 ring-opacity-50":""}
                                        `,onClick:()=>c(l.id),title:m?`${d.title} - 点击开始`:`${d.title} - 需要完成前面的关卡`,children:m?l.number:s.jsx(bt,{className:"text-lg"})}),a&&s.jsxs("div",{className:"absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs bg-red-500 text-white px-1 rounded",children:[l.x,", ",l.y]}),s.jsx("div",{className:"absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:s.jsx("div",{className:"bg-black/70 text-white px-2 py-1 rounded text-sm",children:d.title})})]},l.id):null})]})]})})})},nr=({width:e=800,height:t=600,onSave:n,disabled:r=!1,backgroundImage:a,showReference:o=!1,hideCanvas:i=!1,onClearRef:c,isCompleted:l=!1,onPathsChange:h})=>{const d=u.useRef(null),[m,v]=u.useState(!1),[R,b]=u.useState(5),[j,x]=u.useState("#000000"),[T,C]=u.useState([]),[E,z]=u.useState([]),[K,ie]=u.useState(!1),[se,ge]=u.useState(!1),w=l,$=u.useCallback(y=>{const L=d.current;if(!L)return{x:0,y:0};const H=L.getBoundingClientRect(),oe=L.width/H.width,Z=L.height/H.height;let V,ue;if("touches"in y){const $e=y.touches[0]||y.changedTouches[0];V=$e.clientX,ue=$e.clientY}else V=y.clientX,ue=y.clientY;const ee=(V-H.left)*oe,Ae=(ue-H.top)*Z;return{x:ee,y:Ae}},[]),S=u.useCallback(y=>{if(r||w)return;y.preventDefault(),v(!0);const L=$(y);z([L])},[r,w,$]),k=u.useCallback(y=>{if(!m||r||w)return;y.preventDefault();const L=$(y);z(H=>[...H,L])},[m,r,w,$]),J=u.useCallback(()=>{if(!(!m||r||w)){if(v(!1),E.length>1){const y={points:E,color:j,size:R};C(L=>{const H=[...L,y];return ie(!0),ge(!1),h==null||h(H),H})}z([])}},[m,r,w,E,j,R]),W=u.useCallback((y,L,H,oe)=>{if(!(L.length<2)){y.strokeStyle=H,y.lineWidth=oe,y.lineCap="round",y.lineJoin="round",y.beginPath(),y.moveTo(L[0].x,L[0].y);for(let Z=1;Z<L.length;Z++)y.lineTo(L[Z].x,L[Z].y);y.stroke()}},[]),Q=u.useCallback(()=>{const y=d.current;if(!y)return;const L=y.getContext("2d",{alpha:!0});if(L)if(y.style.backgroundColor="rgba(0,0,0,0)",y.style.background="none",L.clearRect(0,0,y.width,y.height),L.globalCompositeOperation="source-over",a&&o){const H=new Image;H.onload=()=>{L.globalAlpha=.3,L.drawImage(H,0,0,y.width,y.height),L.globalAlpha=1,T.forEach(oe=>{W(L,oe.points,oe.color,oe.size)}),E.length>1&&W(L,E,j,R)},H.src=a}else T.forEach(H=>{W(L,H.points,H.color,H.size)}),E.length>1&&W(L,E,j,R)},[T,E,j,R,a,o,W]),de=u.useCallback(()=>{const y=d.current;if(y){const L=y.getContext("2d",{alpha:!0});L&&L.clearRect(0,0,y.width,y.height)}C([]),z([]),ie(!1),ge(!1),h==null||h([])},[h]),X=u.useCallback(()=>{de()},[de]),pe=u.useCallback(()=>{const y=d.current;if(!y)return;const L=y.toDataURL("image/png");n==null||n(L),ge(!0),Y.success("画作已保存！")},[n]);return u.useEffect(()=>{const y=d.current;if(y){const L=y.getContext("2d",{alpha:!0});L&&(L.globalCompositeOperation="source-over",y.style.backgroundColor="rgba(0,0,0,0)",y.style.background="none",L.clearRect(0,0,y.width,y.height),y.setAttribute("style","background-color: rgba(0,0,0,0) !important; background: none !important; touch-action: none; border-radius: 8px; display: block;"),y.style.setProperty("background-color","rgba(0,0,0,0)","important"),y.style.setProperty("background","none","important"),console.log("Canvas initialized with alpha channel"))}},[]),u.useEffect(()=>{Q()},[Q]),u.useEffect(()=>{const y=d.current;if(y){y.width=e,y.height=t,y.style.width=`${e}px`,y.style.height=`${t}px`;const L=Z=>{if(r||w)return;Z.preventDefault(),Z.stopPropagation();const V=Z.touches[0],ue=y.getBoundingClientRect(),ee=y.width/ue.width,Ae=y.height/ue.height,$e={x:(V.clientX-ue.left)*ee,y:(V.clientY-ue.top)*Ae};v(!0),z([$e])},H=Z=>{if(!m||r||w)return;Z.preventDefault(),Z.stopPropagation();const V=Z.touches[0],ue=y.getBoundingClientRect(),ee=y.width/ue.width,Ae=y.height/ue.height,$e={x:(V.clientX-ue.left)*ee,y:(V.clientY-ue.top)*Ae};z(Xe=>[...Xe,$e])},oe=Z=>{if(Z.preventDefault(),Z.stopPropagation(),!(!m||r||w)){if(v(!1),E.length>1){const V={points:E,color:j,size:R};C(ue=>{const ee=[...ue,V];return ie(!0),ge(!1),h==null||h(ee),ee})}z([])}};return y.addEventListener("touchstart",L,{passive:!1}),y.addEventListener("touchmove",H,{passive:!1}),y.addEventListener("touchend",oe,{passive:!1}),Q(),()=>{y.removeEventListener("touchstart",L),y.removeEventListener("touchmove",H),y.removeEventListener("touchend",oe)}}},[e,t,Q,r,w,m,E,j,R,h]),u.useEffect(()=>{c&&c(de)},[c,de]),u.useEffect(()=>{if(!i&&T.length===0){const y=d.current;if(y){const L=y.getContext("2d",{alpha:!0});L&&L.clearRect(0,0,y.width,y.height)}}},[i,T]),s.jsxs("div",{className:"flex flex-col items-center gap-4",style:{backgroundColor:"transparent"},children:[s.jsxs("div",{className:"flex flex-wrap items-center gap-4 p-4 bg-white rounded-lg shadow-sm border",children:[s.jsxs(fe,{children:[s.jsx("span",{className:"text-sm font-medium",children:"画笔大小:"}),s.jsx(Cn,{min:1,max:20,value:R,onChange:b,style:{width:100},disabled:w}),s.jsxs("span",{className:"text-sm text-gray-500",children:[R,"px"]})]}),s.jsxs(fe,{children:[s.jsx("span",{className:"text-sm font-medium",children:"颜色:"}),s.jsx(Dr,{value:j,onChange:y=>x(y.toHexString()),showText:!0,disabled:w})]}),s.jsxs(fe,{children:[s.jsx(q,{icon:s.jsx(Tn,{}),onClick:X,disabled:!K||se||w,title:"清空",children:"清空"}),s.jsx(q,{icon:s.jsx(Pn,{}),onClick:pe,type:"primary",disabled:!K||se||w,title:"保存",children:"保存"})]})]}),!i&&s.jsx("div",{style:{backgroundColor:"rgba(0,0,0,0)",background:"none",position:"relative",borderRadius:"8px",border:"2px solid #e5e7eb",overflow:"hidden"},children:s.jsx("canvas",{ref:d,width:e,height:t,className:w?"cursor-not-allowed opacity-60":"cursor-crosshair",onMouseDown:S,onMouseMove:k,onMouseUp:J,onMouseLeave:J,style:{touchAction:"none",backgroundColor:"rgba(0,0,0,0)",background:"none",borderRadius:"8px",display:"block",opacity:"1",position:"relative",margin:0,padding:0,border:"none",outline:"none"}})})]})};function rr(e,t){return function(){return e.apply(t,arguments)}}const{toString:Po}=Object.prototype,{getPrototypeOf:Fs}=Object,{iterator:is,toStringTag:ar}=Symbol,ls=(e=>t=>{const n=Po.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Be=e=>(e=e.toLowerCase(),t=>ls(t)===e),cs=e=>t=>typeof t===e,{isArray:ht}=Array,wt=cs("undefined");function Et(e){return e!==null&&!wt(e)&&e.constructor!==null&&!wt(e.constructor)&&_e(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const or=Be("ArrayBuffer");function Ro(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&or(e.buffer),t}const _o=cs("string"),_e=cs("function"),ir=cs("number"),kt=e=>e!==null&&typeof e=="object",Io=e=>e===!0||e===!1,Mt=e=>{if(ls(e)!=="object")return!1;const t=Fs(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ar in e)&&!(is in e)},Lo=e=>{if(!kt(e)||Et(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Ao=Be("Date"),$o=Be("File"),Oo=Be("Blob"),Do=Be("FileList"),Fo=e=>kt(e)&&_e(e.pipe),Mo=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||_e(e.append)&&((t=ls(e))==="formdata"||t==="object"&&_e(e.toString)&&e.toString()==="[object FormData]"))},Uo=Be("URLSearchParams"),[zo,Bo,qo,Ho]=["ReadableStream","Request","Response","Headers"].map(Be),Wo=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ct(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),ht(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{if(Et(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function lr(e,t){if(Et(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const tt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,cr=e=>!wt(e)&&e!==tt;function Cs(){const{caseless:e}=cr(this)&&this||{},t={},n=(r,a)=>{const o=e&&lr(t,a)||a;Mt(t[o])&&Mt(r)?t[o]=Cs(t[o],r):Mt(r)?t[o]=Cs({},r):ht(r)?t[o]=r.slice():t[o]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&Ct(arguments[r],n);return t}const Jo=(e,t,n,{allOwnKeys:r}={})=>(Ct(t,(a,o)=>{n&&_e(a)?e[o]=rr(a,n):e[o]=a},{allOwnKeys:r}),e),Go=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Vo=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Yo=(e,t,n,r)=>{let a,o,i;const c={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&Fs(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Xo=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Ko=e=>{if(!e)return null;if(ht(e))return e;let t=e.length;if(!ir(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Qo=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Fs(Uint8Array)),Zo=(e,t)=>{const r=(e&&e[is]).call(e);let a;for(;(a=r.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},ei=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},ti=Be("HTMLFormElement"),si=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),an=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ni=Be("RegExp"),dr=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Ct(n,(a,o)=>{let i;(i=t(a,o,e))!==!1&&(r[o]=i||a)}),Object.defineProperties(e,r)},ri=e=>{dr(e,(t,n)=>{if(_e(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(_e(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ai=(e,t)=>{const n={},r=a=>{a.forEach(o=>{n[o]=!0})};return ht(e)?r(e):r(String(e).split(t)),n},oi=()=>{},ii=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function li(e){return!!(e&&_e(e.append)&&e[ar]==="FormData"&&e[is])}const ci=e=>{const t=new Array(10),n=(r,a)=>{if(kt(r)){if(t.indexOf(r)>=0)return;if(Et(r))return r;if(!("toJSON"in r)){t[a]=r;const o=ht(r)?[]:{};return Ct(r,(i,c)=>{const l=n(i,a+1);!wt(l)&&(o[c]=l)}),t[a]=void 0,o}}return r};return n(e,0)},di=Be("AsyncFunction"),ui=e=>e&&(kt(e)||_e(e))&&_e(e.then)&&_e(e.catch),ur=((e,t)=>e?setImmediate:t?((n,r)=>(tt.addEventListener("message",({source:a,data:o})=>{a===tt&&o===n&&r.length&&r.shift()()},!1),a=>{r.push(a),tt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",_e(tt.postMessage)),hi=typeof queueMicrotask<"u"?queueMicrotask.bind(tt):typeof process<"u"&&process.nextTick||ur,mi=e=>e!=null&&_e(e[is]),p={isArray:ht,isArrayBuffer:or,isBuffer:Et,isFormData:Mo,isArrayBufferView:Ro,isString:_o,isNumber:ir,isBoolean:Io,isObject:kt,isPlainObject:Mt,isEmptyObject:Lo,isReadableStream:zo,isRequest:Bo,isResponse:qo,isHeaders:Ho,isUndefined:wt,isDate:Ao,isFile:$o,isBlob:Oo,isRegExp:ni,isFunction:_e,isStream:Fo,isURLSearchParams:Uo,isTypedArray:Qo,isFileList:Do,forEach:Ct,merge:Cs,extend:Jo,trim:Wo,stripBOM:Go,inherits:Vo,toFlatObject:Yo,kindOf:ls,kindOfTest:Be,endsWith:Xo,toArray:Ko,forEachEntry:Zo,matchAll:ei,isHTMLForm:ti,hasOwnProperty:an,hasOwnProp:an,reduceDescriptors:dr,freezeMethods:ri,toObjectSet:ai,toCamelCase:si,noop:oi,toFiniteNumber:ii,findKey:lr,global:tt,isContextDefined:cr,isSpecCompliantForm:li,toJSONObject:ci,isAsyncFn:di,isThenable:ui,setImmediate:ur,asap:hi,isIterable:mi};function G(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}p.inherits(G,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:p.toJSONObject(this.config),code:this.code,status:this.status}}});const hr=G.prototype,mr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{mr[e]={value:e}});Object.defineProperties(G,mr);Object.defineProperty(hr,"isAxiosError",{value:!0});G.from=(e,t,n,r,a,o)=>{const i=Object.create(hr);return p.toFlatObject(e,i,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),G.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const fi=null;function Ts(e){return p.isPlainObject(e)||p.isArray(e)}function fr(e){return p.endsWith(e,"[]")?e.slice(0,-2):e}function on(e,t,n){return e?e.concat(t).map(function(a,o){return a=fr(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function pi(e){return p.isArray(e)&&!e.some(Ts)}const xi=p.toFlatObject(p,{},null,function(t){return/^is[A-Z]/.test(t)});function ds(e,t,n){if(!p.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=p.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(j,x){return!p.isUndefined(x[j])});const r=n.metaTokens,a=n.visitor||d,o=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&p.isSpecCompliantForm(t);if(!p.isFunction(a))throw new TypeError("visitor must be a function");function h(b){if(b===null)return"";if(p.isDate(b))return b.toISOString();if(p.isBoolean(b))return b.toString();if(!l&&p.isBlob(b))throw new G("Blob is not supported. Use a Buffer instead.");return p.isArrayBuffer(b)||p.isTypedArray(b)?l&&typeof Blob=="function"?new Blob([b]):Buffer.from(b):b}function d(b,j,x){let T=b;if(b&&!x&&typeof b=="object"){if(p.endsWith(j,"{}"))j=r?j:j.slice(0,-2),b=JSON.stringify(b);else if(p.isArray(b)&&pi(b)||(p.isFileList(b)||p.endsWith(j,"[]"))&&(T=p.toArray(b)))return j=fr(j),T.forEach(function(E,z){!(p.isUndefined(E)||E===null)&&t.append(i===!0?on([j],z,o):i===null?j:j+"[]",h(E))}),!1}return Ts(b)?!0:(t.append(on(x,j,o),h(b)),!1)}const m=[],v=Object.assign(xi,{defaultVisitor:d,convertValue:h,isVisitable:Ts});function R(b,j){if(!p.isUndefined(b)){if(m.indexOf(b)!==-1)throw Error("Circular reference detected in "+j.join("."));m.push(b),p.forEach(b,function(T,C){(!(p.isUndefined(T)||T===null)&&a.call(t,T,p.isString(C)?C.trim():C,j,v))===!0&&R(T,j?j.concat(C):[C])}),m.pop()}}if(!p.isObject(e))throw new TypeError("data must be an object");return R(e),t}function ln(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Ms(e,t){this._pairs=[],e&&ds(e,this,t)}const pr=Ms.prototype;pr.append=function(t,n){this._pairs.push([t,n])};pr.toString=function(t){const n=t?function(r){return t.call(this,r,ln)}:ln;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function gi(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function xr(e,t,n){if(!t)return e;const r=n&&n.encode||gi;p.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(a?o=a(t,n):o=p.isURLSearchParams(t)?t.toString():new Ms(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class cn{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){p.forEach(this.handlers,function(r){r!==null&&t(r)})}}const gr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},yi=typeof URLSearchParams<"u"?URLSearchParams:Ms,bi=typeof FormData<"u"?FormData:null,ji=typeof Blob<"u"?Blob:null,vi={isBrowser:!0,classes:{URLSearchParams:yi,FormData:bi,Blob:ji},protocols:["http","https","file","blob","url","data"]},Us=typeof window<"u"&&typeof document<"u",Ps=typeof navigator=="object"&&navigator||void 0,wi=Us&&(!Ps||["ReactNative","NativeScript","NS"].indexOf(Ps.product)<0),Si=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ni=Us&&window.location.href||"http://localhost",Ei=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Us,hasStandardBrowserEnv:wi,hasStandardBrowserWebWorkerEnv:Si,navigator:Ps,origin:Ni},Symbol.toStringTag,{value:"Module"})),Ce={...Ei,...vi};function ki(e,t){return ds(e,new Ce.classes.URLSearchParams,{visitor:function(n,r,a,o){return Ce.isNode&&p.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function Ci(e){return p.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Ti(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}function yr(e){function t(n,r,a,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),l=o>=n.length;return i=!i&&p.isArray(a)?a.length:i,l?(p.hasOwnProp(a,i)?a[i]=[a[i],r]:a[i]=r,!c):((!a[i]||!p.isObject(a[i]))&&(a[i]=[]),t(n,r,a[i],o)&&p.isArray(a[i])&&(a[i]=Ti(a[i])),!c)}if(p.isFormData(e)&&p.isFunction(e.entries)){const n={};return p.forEachEntry(e,(r,a)=>{t(Ci(r),a,n,0)}),n}return null}function Pi(e,t,n){if(p.isString(e))try{return(t||JSON.parse)(e),p.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Tt={transitional:gr,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,o=p.isObject(t);if(o&&p.isHTMLForm(t)&&(t=new FormData(t)),p.isFormData(t))return a?JSON.stringify(yr(t)):t;if(p.isArrayBuffer(t)||p.isBuffer(t)||p.isStream(t)||p.isFile(t)||p.isBlob(t)||p.isReadableStream(t))return t;if(p.isArrayBufferView(t))return t.buffer;if(p.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return ki(t,this.formSerializer).toString();if((c=p.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return ds(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),Pi(t)):t}],transformResponse:[function(t){const n=this.transitional||Tt.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(p.isResponse(t)||p.isReadableStream(t))return t;if(t&&p.isString(t)&&(r&&!this.responseType||a)){const i=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?G.from(c,G.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ce.classes.FormData,Blob:Ce.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};p.forEach(["delete","get","head","post","put","patch"],e=>{Tt.headers[e]={}});const Ri=p.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),_i=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(i){a=i.indexOf(":"),n=i.substring(0,a).trim().toLowerCase(),r=i.substring(a+1).trim(),!(!n||t[n]&&Ri[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},dn=Symbol("internals");function pt(e){return e&&String(e).trim().toLowerCase()}function Ut(e){return e===!1||e==null?e:p.isArray(e)?e.map(Ut):String(e)}function Ii(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Li=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function gs(e,t,n,r,a){if(p.isFunction(r))return r.call(this,t,n);if(a&&(t=n),!!p.isString(t)){if(p.isString(r))return t.indexOf(r)!==-1;if(p.isRegExp(r))return r.test(t)}}function Ai(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function $i(e,t){const n=p.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,o,i){return this[r].call(this,t,a,o,i)},configurable:!0})})}let Ie=class{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function o(c,l,h){const d=pt(l);if(!d)throw new Error("header name must be a non-empty string");const m=p.findKey(a,d);(!m||a[m]===void 0||h===!0||h===void 0&&a[m]!==!1)&&(a[m||l]=Ut(c))}const i=(c,l)=>p.forEach(c,(h,d)=>o(h,d,l));if(p.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(p.isString(t)&&(t=t.trim())&&!Li(t))i(_i(t),n);else if(p.isObject(t)&&p.isIterable(t)){let c={},l,h;for(const d of t){if(!p.isArray(d))throw TypeError("Object iterator must return a key-value pair");c[h=d[0]]=(l=c[h])?p.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=pt(t),t){const r=p.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return Ii(a);if(p.isFunction(n))return n.call(this,a,r);if(p.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=pt(t),t){const r=p.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||gs(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function o(i){if(i=pt(i),i){const c=p.findKey(r,i);c&&(!n||gs(r,r[c],c,n))&&(delete r[c],a=!0)}}return p.isArray(t)?t.forEach(o):o(t),a}clear(t){const n=Object.keys(this);let r=n.length,a=!1;for(;r--;){const o=n[r];(!t||gs(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const n=this,r={};return p.forEach(this,(a,o)=>{const i=p.findKey(r,o);if(i){n[i]=Ut(a),delete n[o];return}const c=t?Ai(o):String(o).trim();c!==o&&delete n[o],n[c]=Ut(a),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return p.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&p.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[dn]=this[dn]={accessors:{}}).accessors,a=this.prototype;function o(i){const c=pt(i);r[c]||($i(a,i),r[c]=!0)}return p.isArray(t)?t.forEach(o):o(t),this}};Ie.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);p.reduceDescriptors(Ie.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});p.freezeMethods(Ie);function ys(e,t){const n=this||Tt,r=t||n,a=Ie.from(r.headers);let o=r.data;return p.forEach(e,function(c){o=c.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function br(e){return!!(e&&e.__CANCEL__)}function mt(e,t,n){G.call(this,e??"canceled",G.ERR_CANCELED,t,n),this.name="CanceledError"}p.inherits(mt,G,{__CANCEL__:!0});function jr(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new G("Request failed with status code "+n.status,[G.ERR_BAD_REQUEST,G.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Oi(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Di(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const h=Date.now(),d=r[o];i||(i=h),n[a]=l,r[a]=h;let m=o,v=0;for(;m!==a;)v+=n[m++],m=m%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),h-i<t)return;const R=d&&h-d;return R?Math.round(v*1e3/R):void 0}}function Fi(e,t){let n=0,r=1e3/t,a,o;const i=(h,d=Date.now())=>{n=d,a=null,o&&(clearTimeout(o),o=null),e(...h)};return[(...h)=>{const d=Date.now(),m=d-n;m>=r?i(h,d):(a=h,o||(o=setTimeout(()=>{o=null,i(a)},r-m)))},()=>a&&i(a)]}const Wt=(e,t,n=3)=>{let r=0;const a=Di(50,250);return Fi(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,l=i-r,h=a(l),d=i<=c;r=i;const m={loaded:i,total:c,progress:c?i/c:void 0,bytes:l,rate:h||void 0,estimated:h&&c&&d?(c-i)/h:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(m)},n)},un=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},hn=e=>(...t)=>p.asap(()=>e(...t)),Mi=Ce.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ce.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ce.origin),Ce.navigator&&/(msie|trident)/i.test(Ce.navigator.userAgent)):()=>!0,Ui=Ce.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const i=[e+"="+encodeURIComponent(t)];p.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),p.isString(r)&&i.push("path="+r),p.isString(a)&&i.push("domain="+a),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function zi(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Bi(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function vr(e,t,n){let r=!zi(t);return e&&(r||n==!1)?Bi(e,t):t}const mn=e=>e instanceof Ie?{...e}:e;function rt(e,t){t=t||{};const n={};function r(h,d,m,v){return p.isPlainObject(h)&&p.isPlainObject(d)?p.merge.call({caseless:v},h,d):p.isPlainObject(d)?p.merge({},d):p.isArray(d)?d.slice():d}function a(h,d,m,v){if(p.isUndefined(d)){if(!p.isUndefined(h))return r(void 0,h,m,v)}else return r(h,d,m,v)}function o(h,d){if(!p.isUndefined(d))return r(void 0,d)}function i(h,d){if(p.isUndefined(d)){if(!p.isUndefined(h))return r(void 0,h)}else return r(void 0,d)}function c(h,d,m){if(m in t)return r(h,d);if(m in e)return r(void 0,h)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(h,d,m)=>a(mn(h),mn(d),m,!0)};return p.forEach(Object.keys({...e,...t}),function(d){const m=l[d]||a,v=m(e[d],t[d],d);p.isUndefined(v)&&m!==c||(n[d]=v)}),n}const wr=e=>{const t=rt({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:a,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=Ie.from(i),t.url=xr(vr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if(p.isFormData(n)){if(Ce.hasStandardBrowserEnv||Ce.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[h,...d]=l?l.split(";").map(m=>m.trim()).filter(Boolean):[];i.setContentType([h||"multipart/form-data",...d].join("; "))}}if(Ce.hasStandardBrowserEnv&&(r&&p.isFunction(r)&&(r=r(t)),r||r!==!1&&Mi(t.url))){const h=a&&o&&Ui.read(o);h&&i.set(a,h)}return t},qi=typeof XMLHttpRequest<"u",Hi=qi&&function(e){return new Promise(function(n,r){const a=wr(e);let o=a.data;const i=Ie.from(a.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:h}=a,d,m,v,R,b;function j(){R&&R(),b&&b(),a.cancelToken&&a.cancelToken.unsubscribe(d),a.signal&&a.signal.removeEventListener("abort",d)}let x=new XMLHttpRequest;x.open(a.method.toUpperCase(),a.url,!0),x.timeout=a.timeout;function T(){if(!x)return;const E=Ie.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),K={data:!c||c==="text"||c==="json"?x.responseText:x.response,status:x.status,statusText:x.statusText,headers:E,config:e,request:x};jr(function(se){n(se),j()},function(se){r(se),j()},K),x=null}"onloadend"in x?x.onloadend=T:x.onreadystatechange=function(){!x||x.readyState!==4||x.status===0&&!(x.responseURL&&x.responseURL.indexOf("file:")===0)||setTimeout(T)},x.onabort=function(){x&&(r(new G("Request aborted",G.ECONNABORTED,e,x)),x=null)},x.onerror=function(){r(new G("Network Error",G.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let z=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const K=a.transitional||gr;a.timeoutErrorMessage&&(z=a.timeoutErrorMessage),r(new G(z,K.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,x)),x=null},o===void 0&&i.setContentType(null),"setRequestHeader"in x&&p.forEach(i.toJSON(),function(z,K){x.setRequestHeader(K,z)}),p.isUndefined(a.withCredentials)||(x.withCredentials=!!a.withCredentials),c&&c!=="json"&&(x.responseType=a.responseType),h&&([v,b]=Wt(h,!0),x.addEventListener("progress",v)),l&&x.upload&&([m,R]=Wt(l),x.upload.addEventListener("progress",m),x.upload.addEventListener("loadend",R)),(a.cancelToken||a.signal)&&(d=E=>{x&&(r(!E||E.type?new mt(null,e,x):E),x.abort(),x=null)},a.cancelToken&&a.cancelToken.subscribe(d),a.signal&&(a.signal.aborted?d():a.signal.addEventListener("abort",d)));const C=Oi(a.url);if(C&&Ce.protocols.indexOf(C)===-1){r(new G("Unsupported protocol "+C+":",G.ERR_BAD_REQUEST,e));return}x.send(o||null)})},Wi=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,a;const o=function(h){if(!a){a=!0,c();const d=h instanceof Error?h:this.reason;r.abort(d instanceof G?d:new mt(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,o(new G(`timeout ${t} of ms exceeded`,G.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(h=>{h.unsubscribe?h.unsubscribe(o):h.removeEventListener("abort",o)}),e=null)};e.forEach(h=>h.addEventListener("abort",o));const{signal:l}=r;return l.unsubscribe=()=>p.asap(c),l}},Ji=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,a;for(;r<n;)a=r+t,yield e.slice(r,a),r=a},Gi=async function*(e,t){for await(const n of Vi(e))yield*Ji(n,t)},Vi=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},fn=(e,t,n,r)=>{const a=Gi(e,t);let o=0,i,c=l=>{i||(i=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:h,value:d}=await a.next();if(h){c(),l.close();return}let m=d.byteLength;if(n){let v=o+=m;n(v)}l.enqueue(new Uint8Array(d))}catch(h){throw c(h),h}},cancel(l){return c(l),a.return()}},{highWaterMark:2})},us=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Sr=us&&typeof ReadableStream=="function",Yi=us&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Nr=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Xi=Sr&&Nr(()=>{let e=!1;const t=new Request(Ce.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),pn=64*1024,Rs=Sr&&Nr(()=>p.isReadableStream(new Response("").body)),Jt={stream:Rs&&(e=>e.body)};us&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Jt[t]&&(Jt[t]=p.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new G(`Response type '${t}' is not supported`,G.ERR_NOT_SUPPORT,r)})})})(new Response);const Ki=async e=>{if(e==null)return 0;if(p.isBlob(e))return e.size;if(p.isSpecCompliantForm(e))return(await new Request(Ce.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(p.isArrayBufferView(e)||p.isArrayBuffer(e))return e.byteLength;if(p.isURLSearchParams(e)&&(e=e+""),p.isString(e))return(await Yi(e)).byteLength},Qi=async(e,t)=>{const n=p.toFiniteNumber(e.getContentLength());return n??Ki(t)},Zi=us&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:l,responseType:h,headers:d,withCredentials:m="same-origin",fetchOptions:v}=wr(e);h=h?(h+"").toLowerCase():"text";let R=Wi([a,o&&o.toAbortSignal()],i),b;const j=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let x;try{if(l&&Xi&&n!=="get"&&n!=="head"&&(x=await Qi(d,r))!==0){let K=new Request(t,{method:"POST",body:r,duplex:"half"}),ie;if(p.isFormData(r)&&(ie=K.headers.get("content-type"))&&d.setContentType(ie),K.body){const[se,ge]=un(x,Wt(hn(l)));r=fn(K.body,pn,se,ge)}}p.isString(m)||(m=m?"include":"omit");const T="credentials"in Request.prototype;b=new Request(t,{...v,signal:R,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:T?m:void 0});let C=await fetch(b,v);const E=Rs&&(h==="stream"||h==="response");if(Rs&&(c||E&&j)){const K={};["status","statusText","headers"].forEach(w=>{K[w]=C[w]});const ie=p.toFiniteNumber(C.headers.get("content-length")),[se,ge]=c&&un(ie,Wt(hn(c),!0))||[];C=new Response(fn(C.body,pn,se,()=>{ge&&ge(),j&&j()}),K)}h=h||"text";let z=await Jt[p.findKey(Jt,h)||"text"](C,e);return!E&&j&&j(),await new Promise((K,ie)=>{jr(K,ie,{data:z,headers:Ie.from(C.headers),status:C.status,statusText:C.statusText,config:e,request:b})})}catch(T){throw j&&j(),T&&T.name==="TypeError"&&/Load failed|fetch/i.test(T.message)?Object.assign(new G("Network Error",G.ERR_NETWORK,e,b),{cause:T.cause||T}):G.from(T,T&&T.code,e,b)}}),_s={http:fi,xhr:Hi,fetch:Zi};p.forEach(_s,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const xn=e=>`- ${e}`,el=e=>p.isFunction(e)||e===null||e===!1,Er={getAdapter:e=>{e=p.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!el(n)&&(r=_s[(i=String(n)).toLowerCase()],r===void 0))throw new G(`Unknown adapter '${i}'`);if(r)break;a[i||"#"+o]=r}if(!r){const o=Object.entries(a).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(xn).join(`
`):" "+xn(o[0]):"as no adapter specified";throw new G("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:_s};function bs(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new mt(null,e)}function gn(e){return bs(e),e.headers=Ie.from(e.headers),e.data=ys.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Er.getAdapter(e.adapter||Tt.adapter)(e).then(function(r){return bs(e),r.data=ys.call(e,e.transformResponse,r),r.headers=Ie.from(r.headers),r},function(r){return br(r)||(bs(e),r&&r.response&&(r.response.data=ys.call(e,e.transformResponse,r.response),r.response.headers=Ie.from(r.response.headers))),Promise.reject(r)})}const kr="1.11.0",hs={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{hs[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const yn={};hs.transitional=function(t,n,r){function a(o,i){return"[Axios v"+kr+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new G(a(i," has been removed"+(n?" in "+n:"")),G.ERR_DEPRECATED);return n&&!yn[i]&&(yn[i]=!0,console.warn(a(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};hs.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function tl(e,t,n){if(typeof e!="object")throw new G("options must be an object",G.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],i=t[o];if(i){const c=e[o],l=c===void 0||i(c,o,e);if(l!==!0)throw new G("option "+o+" must be "+l,G.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new G("Unknown option "+o,G.ERR_BAD_OPTION)}}const zt={assertOptions:tl,validators:hs},He=zt.validators;let nt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new cn,response:new cn}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const o=a.stack?a.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=rt(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:o}=n;r!==void 0&&zt.assertOptions(r,{silentJSONParsing:He.transitional(He.boolean),forcedJSONParsing:He.transitional(He.boolean),clarifyTimeoutError:He.transitional(He.boolean)},!1),a!=null&&(p.isFunction(a)?n.paramsSerializer={serialize:a}:zt.assertOptions(a,{encode:He.function,serialize:He.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),zt.assertOptions(n,{baseUrl:He.spelling("baseURL"),withXsrfToken:He.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&p.merge(o.common,o[n.method]);o&&p.forEach(["delete","get","head","post","put","patch","common"],b=>{delete o[b]}),n.headers=Ie.concat(i,o);const c=[];let l=!0;this.interceptors.request.forEach(function(j){typeof j.runWhen=="function"&&j.runWhen(n)===!1||(l=l&&j.synchronous,c.unshift(j.fulfilled,j.rejected))});const h=[];this.interceptors.response.forEach(function(j){h.push(j.fulfilled,j.rejected)});let d,m=0,v;if(!l){const b=[gn.bind(this),void 0];for(b.unshift(...c),b.push(...h),v=b.length,d=Promise.resolve(n);m<v;)d=d.then(b[m++],b[m++]);return d}v=c.length;let R=n;for(m=0;m<v;){const b=c[m++],j=c[m++];try{R=b(R)}catch(x){j.call(this,x);break}}try{d=gn.call(this,R)}catch(b){return Promise.reject(b)}for(m=0,v=h.length;m<v;)d=d.then(h[m++],h[m++]);return d}getUri(t){t=rt(this.defaults,t);const n=vr(t.baseURL,t.url,t.allowAbsoluteUrls);return xr(n,t.params,t.paramsSerializer)}};p.forEach(["delete","get","head","options"],function(t){nt.prototype[t]=function(n,r){return this.request(rt(r||{},{method:t,url:n,data:(r||{}).data}))}});p.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(rt(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}nt.prototype[t]=n(),nt.prototype[t+"Form"]=n(!0)});let sl=class Cr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(a=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](a);r._listeners=null}),this.promise.then=a=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(a);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new mt(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Cr(function(a){t=a}),cancel:t}}};function nl(e){return function(n){return e.apply(null,n)}}function rl(e){return p.isObject(e)&&e.isAxiosError===!0}const Is={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Is).forEach(([e,t])=>{Is[t]=e});function Tr(e){const t=new nt(e),n=rr(nt.prototype.request,t);return p.extend(n,nt.prototype,t,{allOwnKeys:!0}),p.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return Tr(rt(e,a))},n}const be=Tr(Tt);be.Axios=nt;be.CanceledError=mt;be.CancelToken=sl;be.isCancel=br;be.VERSION=kr;be.toFormData=ds;be.AxiosError=G;be.Cancel=be.CanceledError;be.all=function(t){return Promise.all(t)};be.spread=nl;be.isAxiosError=rl;be.mergeConfig=rt;be.AxiosHeaders=Ie;be.formToJSON=e=>yr(p.isHTMLForm(e)?new FormData(e):e);be.getAdapter=Er.getAdapter;be.HttpStatusCode=Is;be.default=be;const{Axios:Hl,AxiosError:Wl,CanceledError:Jl,isCancel:Gl,CancelToken:Vl,VERSION:Yl,all:Xl,Cancel:Kl,isAxiosError:Ql,spread:Zl,toFormData:ec,AxiosHeaders:tc,HttpStatusCode:sc,formToJSON:nc,getAdapter:rc,mergeConfig:ac}=be,al="http://localhost:8000/api/v1",Pe=be.create({baseURL:al,timeout:3e4,headers:{"Content-Type":"application/json"}});Pe.interceptors.request.use(e=>{var t;return console.log("API Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url),e},e=>(console.error("API Request Error:",e),Promise.reject(e)));Pe.interceptors.response.use(e=>(console.log("API Response:",e.status,e.config.url),e),e=>{var t,n,r,a,o;return console.error("API Response Error:",{status:(t=e.response)==null?void 0:t.status,statusText:(n=e.response)==null?void 0:n.statusText,data:(r=e.response)==null?void 0:r.data,url:(a=e.config)==null?void 0:a.url,method:(o=e.config)==null?void 0:o.method,message:e.message}),Promise.reject(e)});class ol{async analyzeLines(t){try{console.log("发送线条分析请求:",{canvas_data_length:t.canvas_data.length,paths_count:t.paths.length,paths_sample:t.paths.slice(0,2)});const n=await Pe.post("/games/analyze-lines",t);return console.log("线条分析响应:",n.data),n.data}catch(n){throw console.error("线条分析失败:",n),console.error("请求数据:",t),new Error("线条分析失败，请稍后重试")}}async generateAIImage(t){try{console.log("发送AI生图请求");const n=await Pe.post("/games/generate-ai-image",t);return console.log("AI生图响应:",n.data),n.data}catch(n){return console.error("AI生图失败:",n),{status:"failed",error:"AI生图失败，请稍后重试"}}}async getGameLevels(){try{return(await Pe.get("/games/levels")).data}catch(t){throw console.error("获取游戏级别失败:",t),new Error("获取游戏级别失败")}}async getGameLevel(t){try{return(await Pe.get(`/games/levels/${t}`)).data}catch(n){throw console.error("获取级别详情失败:",n),new Error("获取级别详情失败")}}async getFamousArtworks(){try{return(await Pe.get("/games/artworks")).data}catch(t){throw console.error("获取名画列表失败:",t),new Error("获取名画列表失败")}}async getArtworkDetails(t){try{return(await Pe.get(`/games/artworks/${t}`)).data.data}catch(n){throw console.error("获取名画详情失败:",n),new Error("获取名画详情失败")}}async startGameSession(t,n=1,r){try{return(await Pe.post("/games/sessions/start",{level:t,stage:n,user_id:r})).data}catch(a){throw console.error("开始游戏会话失败:",a),new Error("开始游戏会话失败")}}async submitDrawing(t,n){try{return(await Pe.post(`/games/sessions/${t}/submit`,n)).data}catch(r){throw console.error("提交作品失败:",r),new Error("提交作品失败")}}}const bn=new ol,{Title:At,Text:Ee}=Le,il=({onBack:e,onComplete:t})=>{const[n,r]=u.useState(!1),[a,o]=u.useState(""),[i,c]=u.useState(null),[l,h]=u.useState(null),[d,m]=u.useState(!1),[v,R]=u.useState([]),[b,j]=u.useState(0),x=async E=>{var z;o(E),m(!0);try{const K=await bn.analyzeLines({canvas_data:E,paths:v});if(h(K),r(!0),m(!1),Y.success("风格分析完成！AI正在为您创作艺术作品..."),((z=K.ai_generated_image)==null?void 0:z.status)==="generating")try{const ie=await bn.generateAIImage({canvas_data:E,paths:v});h(se=>se&&{...se,ai_generated_image:ie}),ie.status==="success"?Y.success("AI艺术作品生成完成！"):Y.warning("AI生图暂时不可用，但风格分析已完成")}catch(ie){console.error("AI生图失败:",ie),h(se=>se&&{...se,ai_generated_image:{status:"failed",error:"AI生图服务暂时不可用"}}),Y.warning("AI生图暂时不可用，但风格分析已完成")}}catch(K){console.error("线条分析失败:",K),Y.error("分析失败，但作品已保存"),r(!0),m(!1)}},T=()=>{a?t():Y.warning("请先保存您的作品")},C=()=>{r(!1),o(""),h(null),R([]),j(E=>E+1),i&&i()};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[!n&&s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx(At,{level:2,className:"mb-2 text-purple-600",children:"自由绘画"}),s.jsx(Ee,{className:"text-lg text-gray-600",children:"发挥您的想象力，创作属于您的艺术作品。没有对错，只有表达。让您的创意自由流淌吧！"})]}),s.jsx(q,{icon:s.jsx(Ve,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),n&&s.jsx(re.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mb-6",children:s.jsx(ce,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(yt,{className:"text-2xl text-white"})}),s.jsx(At,{level:3,className:"text-green-600 mb-3",children:"🎉 创作完成！"}),s.jsx(Ee,{className:"text-lg text-gray-700 block mb-6",children:"太棒了！您已经完成了一幅美丽的作品。每一笔都是您创意的体现！"}),d&&s.jsxs("div",{className:"mb-6",children:[s.jsx(Bs,{size:"large"}),s.jsx(Ee,{className:"block mt-2 text-gray-600",children:"正在分析您的线条风格..."})]}),l&&s.jsxs("div",{className:"mb-6 p-4 bg-white rounded-lg border",children:[s.jsxs(At,{level:4,className:"text-blue-600 mb-3",children:[s.jsx(ct,{className:"mr-2"}),"您的作品风格分析"]}),s.jsx("div",{className:"w-full",children:s.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(Ee,{className:"block text-sm text-gray-600 mb-1",children:"复杂度"}),s.jsxs(Ee,{className:"font-medium text-lg",children:[(l.line_features.complexity*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(Ee,{className:"block text-sm text-gray-600 mb-1",children:"节奏感"}),s.jsxs(Ee,{className:"font-medium text-lg",children:[(l.line_features.rhythm*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(Ee,{className:"block text-sm text-gray-600 mb-1",children:"平滑度"}),s.jsxs(Ee,{className:"font-medium text-lg",children:[(l.line_features.smoothness*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(Ee,{className:"block text-sm text-gray-600 mb-1",children:"曲线特征"}),s.jsx(Ee,{className:"font-medium text-lg",children:l.line_features.dominant_curves?"是":"否"})]})]})}),l.ai_generated_image&&s.jsxs("div",{className:"mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200",children:[s.jsx(At,{level:4,className:"text-purple-600 mb-3 text-center",children:"🎨 AI为您创作的艺术作品"}),l.ai_generated_image.status==="success"&&l.ai_generated_image.image_url&&s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx(Ee,{className:"block text-lg font-medium text-gray-700 mb-3",children:"📝 您的原创作品"}),s.jsx(qs,{src:a,alt:"用户原创作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-gray-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看原图"})}})]}),s.jsxs("div",{className:"text-center",children:[s.jsx(Ee,{className:"block text-lg font-medium text-purple-600 mb-3",children:"🤖 AI 创作版本"}),s.jsx(qs,{src:l.ai_generated_image.image_url,alt:"AI生成的艺术作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-purple-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看AI作品"})}})]})]}),l.ai_generated_image.status==="failed"&&s.jsxs("div",{className:"text-center text-gray-500",children:[s.jsx(Ee,{children:"😔 AI创作暂时不可用"}),l.ai_generated_image.error&&s.jsx(Ee,{className:"text-xs block mt-1",children:l.ai_generated_image.error})]}),l.ai_generated_image.status==="generating"&&s.jsx("div",{className:"text-center py-8",children:s.jsxs("div",{className:"relative",children:[s.jsx(Bs,{size:"large"}),s.jsxs("div",{className:"mt-4",children:[s.jsx(Ee,{className:"block text-lg font-medium text-purple-600",children:"🎨 AI正在为您创作艺术作品"}),s.jsx(Ee,{className:"block mt-2 text-gray-600",children:"请稍候，这可能需要10-30秒..."}),s.jsxs("div",{className:"mt-3 flex justify-center items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce"}),s.jsx("div",{className:"w-2 h-2 bg-pink-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})})]})]}),s.jsxs(fe,{size:"large",children:[s.jsx(q,{type:"primary",size:"large",icon:s.jsx(yt,{}),onClick:T,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(q,{size:"large",icon:s.jsx(dt,{}),onClick:C,className:"h-12 px-8",children:"重新创作"})]})]})})}),!n&&s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(nr,{width:768,height:1024,onSave:x,hideCanvas:!1,onClearRef:c,isCompleted:!1,onPathsChange:R},b)})]})})})},{Title:$t,Text:Ot}=Le,ll=[{id:"triangle",name:"三角形",description:"绘制一个三角形",instruction:"画三条直线，让它们相互连接形成三角形",difficulty:1,type:"straight"},{id:"square",name:"正方形",description:"绘制一个正方形",instruction:"画四条相等的直线，形成四个直角",difficulty:1,type:"straight"},{id:"rectangle",name:"长方形",description:"绘制一个长方形",instruction:"画四条直线，对边相等且平行",difficulty:1,type:"straight"},{id:"pentagon",name:"五边形",description:"绘制一个五边形",instruction:"画五条直线，形成一个封闭的五边形",difficulty:2,type:"straight"},{id:"hexagon",name:"六边形",description:"绘制一个六边形",instruction:"画六条直线，形成一个封闭的六边形",difficulty:2,type:"straight"},{id:"circle",name:"圆形",description:"绘制一个圆形",instruction:"画一条连续的曲线，让起点和终点相接",difficulty:1,type:"curved"},{id:"oval",name:"椭圆形",description:"绘制一个椭圆形",instruction:"画一个拉长的圆形，像鸡蛋的形状",difficulty:2,type:"curved"},{id:"heart",name:"心形",description:"绘制一个心形",instruction:"画两个圆弧在顶部，底部汇聚成一个点",difficulty:3,type:"curved"},{id:"star",name:"星形",description:"绘制一个五角星",instruction:"画五个尖角，每个尖角之间用直线或曲线连接",difficulty:3,type:"curved"}],jn=({onBack:e,onComplete:t,shapeType:n})=>{const[r,a]=u.useState(0),[o,i]=u.useState(!1),[c,l]=u.useState(0),[h,d]=u.useState(!1),m=ll.filter(z=>z.type===n),v=m[r],R=z=>{const ie=(4-v.difficulty)*10,se=Math.floor(Math.random()*20),ge=Math.min(100,60+ie+se);l(ge),i(!0),Y.success(`太棒了！您的${v.name}得分：${ge}分`)},b=()=>{t(c)},j=()=>{r<m.length-1?(a(z=>z+1),i(!1),l(0),d(!1)):b()},x=()=>{i(!1),l(0)},T=()=>{d(!h)},C=z=>{switch(z){case 1:return"green";case 2:return"blue";case 3:return"purple";default:return"gray"}},E=z=>{switch(z){case 1:return"简单";case 2:return"中等";case 3:return"困难";default:return"未知"}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs($t,{level:2,className:"mb-2 text-purple-600",children:[n==="straight"?"直线图形":"曲线图形","绘制"]}),s.jsx(Ot,{className:"text-lg text-gray-600",children:n==="straight"?"学习绘制各种直线构成的几何图形":"练习绘制优美的曲线图形"})]}),s.jsx(q,{icon:s.jsx(Ve,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),s.jsx(ce,{className:"mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsxs($t,{level:4,className:"text-blue-600 mb-0",children:[v.name," (",r+1,"/",m.length,")"]}),s.jsx(Qe,{color:C(v.difficulty),children:E(v.difficulty)})]}),s.jsx(Ot,{className:"text-gray-700 block mb-2",children:v.description}),h&&s.jsx(re.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2",children:s.jsxs(Ot,{className:"text-yellow-800",children:["💡 提示：",v.instruction]})})})]}),s.jsxs(q,{icon:s.jsx(Fr,{}),onClick:T,type:h?"primary":"default",children:[h?"隐藏":"显示","提示"]})]})}),s.jsx(ce,{className:"mb-6 bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx($t,{level:4,className:"text-green-600 mb-3",children:"🎯 绘画要点"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{children:n==="straight"?"保持线条笔直":"保持曲线流畅"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"注意图形的对称性"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"确保线条闭合"})]})]})]})}),s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(nr,{width:800,height:600,onSave:R})}),o&&s.jsx(re.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mt-6",children:s.jsx(ce,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(yt,{className:"text-2xl text-white"})}),s.jsxs($t,{level:3,className:"text-green-600 mb-3",children:["🎉 ",v.name,"绘制完成！"]}),s.jsxs("div",{className:"mb-4",children:[s.jsx(Ot,{className:"text-lg text-gray-700 block mb-2",children:"绘制评分"}),s.jsx(Nn,{percent:c,strokeColor:{"0%":"#108ee9","100%":"#87d068"},className:"max-w-md mx-auto"})]}),s.jsxs(fe,{size:"large",children:[r<m.length-1?s.jsx(q,{type:"primary",size:"large",icon:s.jsx(yt,{}),onClick:j,className:"h-12 px-8 bg-blue-500 hover:bg-blue-600",children:"下一个图形"}):s.jsx(q,{type:"primary",size:"large",icon:s.jsx(yt,{}),onClick:b,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(q,{size:"large",icon:s.jsx(dt,{}),onClick:x,className:"h-12 px-8",children:"重新绘制"})]})]})})})]})})})},lt={accuracyThreshold:.7,tolerance:20,animationSpeed:.6,staticTraceDisplayTime:3e3},xt={minCanvasSize:350,maxCanvasSize:700,sizeRatios:{small:.45,medium:.5,large:.55}},vn=({guidePaths:e,originalCanvasSize:t,levelStage:n="L1-3",onPathComplete:r})=>{const a=n==="L1-4",o=u.useRef(null),[i,c]=u.useState(0),[l,h]=u.useState(!1),[d,m]=u.useState([]),[v,R]=u.useState([]),[b,j]=u.useState({}),[x,T]=u.useState(0),[C,E]=u.useState("entering"),[z,K]=u.useState(.1),[ie,se]=u.useState(1),[ge,w]=u.useState(0),[$,S]=u.useState(1),[k,J]=u.useState(""),[W,Q]=u.useState(!1),[de,X]=u.useState(""),[pe,y]=u.useState(""),[L,H]=u.useState(!1),[oe,Z]=u.useState(""),[V,ue]=u.useState(null),[ee,Ae]=u.useState({width:800,height:600}),[$e,Xe]=u.useState(""),[Pt,Rt]=u.useState(""),[Re,ot]=u.useState(null),[je,it]=u.useState("guide-animation"),[Je,ft]=u.useState(null);u.useEffect(()=>{const f=new Image;f.onload=()=>{ue(f)},f.src=`/${n}/arrow.png`},[n]),u.useEffect(()=>{Xe(""),Rt("");const f=P=>new Promise((I,_)=>{const B=new Image;B.onload=()=>I(B),B.onerror=()=>_(new Error(`Failed to load ${P}`)),B.src=P});(async()=>{const P=["png","jpg","jpeg"];let I=null,_="";for(const O of P)try{const A=`/${n}/bg.${O}`;I=await f(A),_=A;break}catch{}if(I&&_){Xe(_);const A=(()=>{const ae=window.innerHeight;let me;return ae<=600?me=ae*xt.sizeRatios.small:ae<=900?me=ae*xt.sizeRatios.medium:me=ae*xt.sizeRatios.large,me=Math.max(xt.minCanvasSize,me),me=Math.min(xt.maxCanvasSize,me),me})(),F=I.width/I.height;let U,ne;F>=1?(U=A,ne=A/F,ne>A&&(ne=A,U=A*F)):(ne=A,U=A*F,U>A&&(U=A,ne=A/F)),Ae({width:Math.round(U),height:Math.round(ne)})}else console.error("Failed to load canvas background image in any format"),Ae({width:400,height:300});const B=["jpg","jpeg","png"];for(const O of B)try{const A=`/${n}/full.${O}`;await f(A),Rt(A);break}catch{}})()},[n]);const ms=f=>f,[qe,fs]=u.useState([]);u.useEffect(()=>{if(e.length>0){const f=ms(e);fs(f);let N=0;for(;N<f.length;){const P=f[N];if(!P||P.points.length>=2)break;R(I=>[...I,P.id]),r(P.id,[]),N++}N!==i&&c(N)}},[e]);const he=qe[i];u.useEffect(()=>{qe.length>0&&he&&(Re&&(clearTimeout(Re),ot(null)),a&&Je&&(clearTimeout(Je),ft(null)),m([]),T(0),a&&it("guide-animation"))},[qe,i,he]),u.useEffect(()=>{if(C==="entering"){const f=Date.now(),N=3e3,P=()=>{const I=Date.now()-f,_=Math.min(I/N,1),O=(F=>F<.5?4*F*F*F:1-Math.pow(-2*F+2,3)/2)(_),A=.1+(1-.1)*O;K(A),_<1?requestAnimationFrame(P):E("instruction")};requestAnimationFrame(P)}},[C]),u.useEffect(()=>{if(C==="instruction"){const f="Let’s create a beautiful painting together. Try to follow the yellow lines closely and gently trace along with the brush. You’ve got this—take your time.";(async()=>{let P=f;try{const I=await fetch(`/${n}/start.txt`);if(I.ok){const _=await I.text();_.trim()&&(P=_.trim())}}catch{console.log(`未找到 ${n}/start.txt，使用默认引导文本`)}if(!P){Q(!1),E("active");return}X(P),Q(!0),J(""),setTimeout(()=>{let I=0;const _=100,B=()=>{I<P.length?(J(P.slice(0,I+1)),I++,setTimeout(B,_)):setTimeout(()=>{Q(!1),X(""),E("active")},2e3)};B()},200)})()}},[C,n]),u.useEffect(()=>{C==="completing"&&(async()=>{let N="";try{const P=await fetch(`/${n}/end.txt`);if(P.ok){const I=await P.text();I.trim()&&(N=I.trim())}}catch{console.log(`未找到 ${n}/end.txt，跳过结束鼓励语`)}N&&setTimeout(()=>{Z(N),H(!0),y(""),setTimeout(()=>{let P=0;const I=80,_=()=>{P<N.length?(y(N.slice(0,P+1)),P++,setTimeout(_,I)):setTimeout(()=>{H(!1),y(""),Z("")},3e3)};_()},200)},2e3)})()},[C,n]),u.useEffect(()=>{if(C==="completing"){const f=Date.now(),N=2e3,P=()=>{const I=Date.now()-f,_=Math.min(I/N,1);se(1-_),w(_),_<1?requestAnimationFrame(P):E("showing")};requestAnimationFrame(P)}},[C]),u.useEffect(()=>{if(C==="showing"){if(a)return;{const f=setTimeout(()=>{E("exiting")},3e3);return()=>clearTimeout(f)}}},[C,a]),u.useEffect(()=>{if(C==="exiting"){if(a)return;const f=Date.now(),N=3e3,P=()=>{const I=Date.now()-f,_=Math.min(I/N,1),O=(U=>U<.5?2*U*U:-1+(4-2*U)*U)(_);S(1-O*.85);const A=.9;if(_>A){const U=(_-A)/(1-A);w(1-U)}else w(1);const F=.3;if(_>F)se(0);else{const U=_/F;se(1-U)}_<1&&requestAnimationFrame(P)};requestAnimationFrame(P)}},[C,a]),u.useEffect(()=>{const f=o.current;if(!f)return;const N=B=>{var O;(O=B.target)!=null&&O.closest("canvas")&&B.preventDefault()},P=B=>{var me;const O=((me=document.querySelector("[data-guide-stage]"))==null?void 0:me.getAttribute("data-guide-stage"))||je;if(B.preventDefault(),B.stopPropagation(),a&&(O==="guide-animation"||O==="static-trace"))return;Re&&(clearTimeout(Re),ot(null)),h(!0);const A=B.touches[0],F=f.getBoundingClientRect(),U=f.width/F.width,ne=f.height/F.height,ae={x:(A.clientX-F.left)*U,y:(A.clientY-F.top)*ne};m([ae])},I=B=>{var ve;B.preventDefault(),B.stopPropagation();const O=((ve=document.querySelector("[data-guide-stage]"))==null?void 0:ve.getAttribute("data-guide-stage"))||je;if(a&&(O==="guide-animation"||O==="static-trace")||!l)return;const A=B.touches[0],F=f.getBoundingClientRect(),U=f.width/F.width,ne=f.height/F.height,ae={x:(A.clientX-F.left)*U,y:(A.clientY-F.top)*ne},me=[...d,ae];m(me)},_=B=>{B.preventDefault(),B.stopPropagation(),Ue()};return f.addEventListener("touchstart",P,{passive:!1}),f.addEventListener("touchmove",I,{passive:!1}),f.addEventListener("touchend",_,{passive:!1}),document.addEventListener("touchmove",N,{passive:!1}),document.addEventListener("wheel",N,{passive:!1}),()=>{f.removeEventListener("touchstart",P),f.removeEventListener("touchmove",I),f.removeEventListener("touchend",_),document.removeEventListener("touchmove",N),document.removeEventListener("wheel",N)}},[l,d,Re,je,a]),u.useEffect(()=>()=>{Re&&clearTimeout(Re),a&&Je&&clearTimeout(Je)},[Re,Je,a]),u.useEffect(()=>{if(!he||C!=="active")return;let f,N=!0;if(a)je==="guide-animation"&&(()=>{if(!N||je!=="guide-animation")return;const I=he.points.length,_=3e3,B=Math.max(0,(I-10)*150),O=(_+B)/lt.animationSpeed,A=Date.now(),F=()=>{if(!N||je!=="guide-animation")return;const ne=Date.now()-A;let ae=Math.min(ne/O,1);const ve=(Se=>Se<.5?2*Se*Se:-1+(4-2*Se)*Se)(ae);T(ve),ae<1?f=requestAnimationFrame(F):N&&he&&!v.includes(he.id)&&(it("static-trace"),T(1))};f=requestAnimationFrame(F)})();else{const P=()=>{if(!N)return;const I=he.points.length,_=3e3,B=Math.max(0,(I-10)*150),O=(_+B)/lt.animationSpeed,A=Date.now(),F=()=>{if(!N)return;const ne=Date.now()-A;let ae=Math.min(ne/O,1);const ve=(Se=>Se<.5?2*Se*Se:-1+(4-2*Se)*Se)(ae);T(ve),ae<1?f=requestAnimationFrame(F):N&&he&&!v.includes(he.id)&&setTimeout(()=>{N&&(T(0),P())},500)};f=requestAnimationFrame(F)};P()}return()=>{N=!1,f&&cancelAnimationFrame(f)}},[i,he,C,je,a]),u.useEffect(()=>{if(!(!a||!he||C!=="active")&&je==="static-trace"){const f=setTimeout(()=>{v.includes(he.id)||(it("waiting-user"),T(0))},lt.staticTraceDisplayTime);return ft(f),()=>{clearTimeout(f)}}},[je,he,a,C,v]),u.useEffect(()=>{const f=o.current;f&&(f.width=ee.width,f.height=ee.height,f.style.width=`${ee.width}px`,f.style.height=`${ee.height}px`)},[ee]),u.useEffect(()=>{const f=o.current;if(!f)return;const N=f.getContext("2d");if(!N)return;N.clearRect(0,0,f.width,f.height),P();function P(){!he||!N||(v.forEach(I=>{const _=b[I];_&&_.length>0&&te(N,_)}),he&&(a?je==="guide-animation"?M(N,he):je==="static-trace"&&D(N,he):M(N,he)),d.length>0&&te(N,d))}},[he,d,v,b,x,ee,a?je:null]);const g=(f,N,P,I,_,B,O=15)=>{const A=I-N,F=_-P;if(!V){const ae=Math.atan2(F,A);f.fillStyle=B,f.strokeStyle=B,f.lineWidth=2,f.beginPath(),f.moveTo(I,_),f.lineTo(I-O*Math.cos(ae-Math.PI/5),_-O*Math.sin(ae-Math.PI/5)),f.lineTo(I-O*Math.cos(ae+Math.PI/5),_-O*Math.sin(ae+Math.PI/5)),f.closePath(),f.fill(),f.stroke();return}const U=Math.atan2(F,A),ne=O*2;f.save(),f.translate(I,_),f.rotate(U),f.drawImage(V,-ne/2,-ne/2,ne,ne),f.restore()},M=(f,N)=>{if(N.points.length<2)return;const P=(t==null?void 0:t.width)||800,I=(t==null?void 0:t.height)||600,_=ee.width/P,B=ee.height/I,O=N.points.map(U=>({x:U.x*_,y:U.y*B}));f.strokeStyle="#FFD700",f.lineWidth=8,f.lineCap="round",f.lineJoin="round",f.setLineDash([]);const A=O.length,F=Math.floor(A*x);if(f.beginPath(),F>0){f.moveTo(O[0].x,O[0].y);for(let U=1;U<F;U++)f.lineTo(O[U].x,O[U].y);if(F<A){const U=O[F-1],ne=O[F],ae=A*x-F,me=U.x+(ne.x-U.x)*ae,ve=U.y+(ne.y-U.y)*ae;f.lineTo(me,ve)}}if(f.stroke(),f.setLineDash([]),x>.1&&F>0){let U,ne,ae,me;if(F<A){const ve=O[F-1],Se=O[F],zs=A*x-F;U=ve.x+(Se.x-ve.x)*zs,ne=ve.y+(Se.y-ve.y)*zs,ae=ve.x,me=ve.y}else U=O[A-1].x,ne=O[A-1].y,ae=O[Math.max(0,A-2)].x,me=O[Math.max(0,A-2)].y;g(f,ae,me,U,ne,"#FFD700",20)}},D=(f,N)=>{if(N.points.length<2)return;const P=(t==null?void 0:t.width)||800,I=(t==null?void 0:t.height)||600,_=ee.width/P,B=ee.height/I,O=N.points.map(A=>({x:A.x*_,y:A.y*B}));f.strokeStyle="#888888",f.lineWidth=4,f.lineCap="round",f.lineJoin="round",f.setLineDash([10,5]),f.beginPath(),f.moveTo(O[0].x,O[0].y);for(let A=1;A<O.length;A++)f.lineTo(O[A].x,O[A].y);f.stroke(),f.setLineDash([])},te=(f,N)=>{N.length<2||(f.strokeStyle="#000000",f.lineWidth=4,f.lineCap="round",f.lineJoin="round",xe(f,N))},xe=(f,N)=>{if(!(N.length<2)){if(f.beginPath(),f.moveTo(N[0].x,N[0].y),N.length===2)f.lineTo(N[1].x,N[1].y);else{for(let I=1;I<N.length-1;I++){const _=N[I],B=N[I+1],O=(_.x+B.x)/2,A=(_.y+B.y)/2;f.quadraticCurveTo(_.x,_.y,O,A)}const P=N[N.length-1];f.lineTo(P.x,P.y)}f.stroke()}},le=f=>{const N=o.current;if(!N)return{x:0,y:0};const P=N.getBoundingClientRect(),I=N.width/P.width,_=N.height/P.height,B=(f.clientX-P.left)*I,O=(f.clientY-P.top)*_;return{x:B,y:O}},ye=f=>{if(f.preventDefault(),f.stopPropagation(),a&&(je==="guide-animation"||je==="static-trace"))return;Re&&(clearTimeout(Re),ot(null)),h(!0);const N=le(f);m([N])},ke=f=>{if(f.preventDefault(),f.stopPropagation(),a&&(je==="guide-animation"||je==="static-trace")||!l)return;const N=le(f),P=[...d,N];m(P)},Ue=f=>{if(f&&(f.preventDefault(),f.stopPropagation()),!l||!he)return;if(h(!1),Oe(d,he.points)>lt.accuracyThreshold)if(Re&&(clearTimeout(Re),ot(null)),r(he.id,d),R(P=>[...P,he.id]),j(P=>({...P,[he.id]:d})),i<qe.length-1){let P=i+1;for(;P<qe.length;){const I=qe[P];if(!I||I.points.length>=2)break;R(_=>[..._,I.id]),r(I.id,[]),P++}P<qe.length?(c(P),m([]),T(0)):setTimeout(()=>{E("completing")},500)}else setTimeout(()=>{E("completing")},500);else m([]),a&&(it("guide-animation"),T(0),Je&&(clearTimeout(Je),ft(null)))},Ge=f=>{if(f.length<2)return f;const N=(t==null?void 0:t.width)||800,P=(t==null?void 0:t.height)||600,I=ee.width/N,_=ee.height/P,B=f.map(F=>({x:F.x*I,y:F.y*_})),O=[],A=5;for(let F=0;F<B.length-1;F++){const U=B[F],ne=B[F+1],ae=Math.sqrt(Math.pow(ne.x-U.x,2)+Math.pow(ne.y-U.y,2)),me=Math.ceil(ae/A);for(let ve=0;ve<=me;ve++){const Se=ve/me;O.push({x:U.x+(ne.x-U.x)*Se,y:U.y+(ne.y-U.y)*Se})}}return O},Oe=(f,N)=>{if(f.length===0||N.length===0||f.length<2)return 0;const P=Ge(N);let I=0,_=0;for(const U of f){let ne=1/0;for(const ae of P){const me=Math.sqrt(Math.pow(U.x-ae.x,2)+Math.pow(U.y-ae.y,2));ne=Math.min(ne,me)}ne<=lt.tolerance&&(I+=ne,_++)}if(_===0)return 0;const B=I/_,O=_/f.length;return Math.max(0,1-B/lt.tolerance)*O};return s.jsx("div",{className:"trace-practice min-h-screen","data-guide-stage":je,style:{background:"transparent"},children:s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"relative",style:{transform:"translateY(-120px) translateX(10px)"},children:[W&&s.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-black bg-opacity-75 text-white px-8 py-6 rounded-lg shadow-lg w-[800px] mx-4 relative",children:[s.jsx("div",{className:"text-2xl font-medium leading-relaxed text-left whitespace-pre-wrap opacity-0 pointer-events-none",children:de}),s.jsxs("div",{className:"text-2xl font-medium leading-relaxed text-left whitespace-pre-wrap absolute inset-0 px-8 py-6",children:[k,s.jsx("span",{className:"animate-pulse",children:"|"})]})]})}),L&&s.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-green-600 bg-opacity-90 text-white px-8 py-6 rounded-lg shadow-lg w-[800px] mx-4 border-2 border-green-400 relative",children:[s.jsxs("div",{className:"text-2xl font-medium leading-relaxed text-left whitespace-pre-wrap opacity-0 pointer-events-none",children:["🎉 ",oe]}),s.jsxs("div",{className:"text-2xl font-medium leading-relaxed text-left whitespace-pre-wrap absolute inset-0 px-8 py-6",children:["🎉 ",pe,s.jsx("span",{className:"animate-pulse",children:"|"})]})]})}),s.jsxs("div",{className:"relative flex items-center justify-center",children:[$e&&s.jsx("div",{style:{width:`${ee.width}px`,height:`${ee.height}px`,transform:`scale(${z})`,opacity:ie,transition:"none"},children:s.jsx("img",{src:$e,alt:"背景图片",className:"object-contain w-full h-full"})}),s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",style:{transform:`scale(${z})`,opacity:ie,transition:"none"},children:s.jsx("canvas",{ref:o,width:ee.width,height:ee.height,className:"bg-transparent",style:{touchAction:"none",userSelect:"none",width:`${ee.width}px`,height:`${ee.height}px`,position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:C==="active"?ye:void 0,onMouseMove:C==="active"?ke:void 0,onMouseUp:C==="active"?Ue:void 0,onMouseLeave:C==="active"?()=>h(!1):void 0})}),(C==="completing"||C==="showing"||C==="exiting")&&Pt&&s.jsx("div",{className:"absolute inset-0 flex flex-col items-center justify-center",style:{opacity:ge,transform:`scale(${$})`,transition:"none"},children:s.jsx("img",{src:Pt,alt:"完整图片",className:"object-contain",style:{width:`${ee.width}px`,height:`${ee.height}px`},onError:f=>{console.error("奖励图片加载失败"),f.currentTarget.style.display="none"}})})]})]})})})},js=()=>{const e=Ye(),{level:t,stage:n}=La(),[r,a]=u.useState("level-select"),[o,i]=u.useState(""),[c,l]=u.useState(""),[h,d]=u.useState("free-draw"),[m,v]=u.useState(()=>Eo()),[R,b]=u.useState(()=>ko()),[j,x]=u.useState(null),[T,C]=u.useState(!1),E=Ze.useCallback(async(X,pe)=>{var y,L,H;C(!0);try{if(X==="trace"&&pe)try{console.log(`尝试加载轨迹文件: /${pe}/trace.json`);const V=await fetch(`/${pe}/trace.json`);if(!V.ok)throw new Error(`HTTP ${V.status}: 无法加载 ${pe}/trace.json`);const ue=V.headers.get("content-type");if(!ue||!ue.includes("application/json"))throw new Error(`文件不是JSON格式: ${ue}`);const ee=await V.json();console.log("成功加载轨迹数据:",ee);const Ae=((y=ee.strokes)==null?void 0:y.map(($e,Xe)=>({id:`path_${Xe}`,points:$e.points||[]})))||[];console.log(`转换后的引导路径数量: ${Ae.length}`),x({sessionId:`trace_${Date.now()}`,guidePaths:Ae,guideImage:ee.backgroundImage||"",artworkName:"trace",canvasSize:ee.canvasSize||{width:800,height:600},levelStage:pe}),C(!1),console.log("轨迹练习数据设置成功");return}catch(V){if(console.warn(`从文件加载失败: ${(V==null?void 0:V.message)||V}`),console.warn(`文件路径: /${pe}/trace.json`),(L=V==null?void 0:V.message)!=null&&L.includes("404")||(H=V==null?void 0:V.message)!=null&&H.includes("HTTP 404")){Y.warning(`${pe} 目录下没有 trace.json 文件，该关卡暂不支持图像描线功能`),C(!1);return}console.warn("尝试使用后端API作为备选方案")}if(X==="trace")throw new Error("trace类型轨迹文件加载失败，且没有后端支持");const oe=await fetch("/api/v1/games/trace/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({artwork_name:X,difficulty:"medium"})});if(!oe.ok)throw new Error("启动描线练习失败");const Z=await oe.json();if(Z.success)x({sessionId:Z.session_id,guidePaths:Z.guide_paths,guideImage:Z.guide_image,artworkName:Z.artwork_name,canvasSize:Z.canvas_size,levelStage:pe});else throw new Error(Z.error||"启动描线练习失败")}catch(oe){console.error("启动描线练习失败:",oe),Y.error("启动描线练习失败，请重试")}finally{C(!1)}},[]),z=Ze.useCallback(X=>{E("trace",X)},[E]);Ze.useEffect(()=>{if(t&&n){let X=n;!n.includes("-")&&t&&(X=`${t}-${n}`),i(t),l(X),a("playing");const pe=K.find(L=>L.id===t),y=pe==null?void 0:pe.stages.find(L=>L.id===X);y&&(d(y.type),y.type==="image-trace"&&z(X))}else t?(i(t),a("stage-select")):a("level-select")},[t,n,z]);const K=os,ie=X=>{i(X),a("stage-select"),e(`/game/${X}`)},se=Ze.useCallback(X=>{l(X),a("playing"),e(`/game/${o}/${X}`)},[o,e]),ge=()=>{a("level-select"),i(""),l(""),e("/game")},w=()=>{a("stage-select"),l(""),e(`/game/${o}`)},$=()=>{v(["level-1"]),b(["L1-1"]),localStorage.removeItem("memorybrush-unlocked-levels"),localStorage.removeItem("memorybrush-unlocked-stages"),Y.success("游戏进度已重置")},S=async(X,pe)=>{if(j)try{const y=await fetch("/api/v1/games/trace/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:j.sessionId,user_paths:pe,completed_path_id:X})});if(!y.ok)throw new Error("提交描线进度失败");(await y.json()).success&&console.log("路径完成:",X)}catch(y){console.error("提交描线进度失败:",y)}},k=()=>{Y.success("恭喜！描线练习完成！"),w()},J=()=>{Y.success("恭喜完成练习！"),setTimeout(()=>{w()},2e3)},W=X=>{Y.success(`恭喜完成！得分：${X}分`),setTimeout(()=>{w()},2e3)},Q=()=>{switch(h){case"free-draw":return s.jsx(il,{onBack:w,onComplete:J});case"trace":return T?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在准备描线练习..."})]})}):j?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:w,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(vn,{guidePaths:j.guidePaths,guideImage:j.guideImage,originalCanvasSize:j.canvasSize,levelStage:j.levelStage,onPathComplete:S,onAllComplete:k})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"描线练习数据加载失败"}),s.jsx("button",{onClick:w,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"image-trace":return T?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在从图片抽取线条..."})]})}):j?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:w,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(vn,{guidePaths:j.guidePaths,guideImage:j.guideImage,originalCanvasSize:j.canvasSize,levelStage:j.levelStage,onPathComplete:S,onAllComplete:k})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"图像线条抽取失败"}),s.jsx("button",{onClick:w,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"shape-straight":return s.jsx(jn,{onBack:w,onComplete:W,shapeType:"straight"});case"shape-curved":return s.jsx(jn,{onBack:w,onComplete:W,shapeType:"curved"});default:return null}},de=()=>K.find(X=>X.id===o);return s.jsxs("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:[r==="level-select"&&s.jsx(No,{onSelectLevel:ie,unlockedLevels:m,onResetProgress:$}),r==="stage-select"&&de()&&s.jsx(Co,{level:de(),onSelectStage:se,onBack:ge,unlockedStages:R}),r==="playing"&&Q()]})},{Title:cl,Paragraph:dl}=Le,ul=()=>{const e=Ye();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(En,{className:"text-4xl text-white"})}),s.jsx(cl,{level:1,className:"text-blue-600 mb-4",children:"个人资料"}),s.jsxs(dl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["个人资料功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造个性化的用户体验。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(q,{size:"large",icon:s.jsx(Ve,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:hl,Paragraph:ml}=Le,fl=()=>{const e=Ye();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(As,{className:"text-4xl text-white"})}),s.jsx(hl,{level:1,className:"text-green-600 mb-4",children:"设置"}),s.jsxs(ml,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["设置功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您准备贴心的个性化设置选项。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(q,{size:"large",icon:s.jsx(Ve,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:pl,Paragraph:xl}=Le,gl=()=>{const e=Ye();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(ct,{className:"text-4xl text-white"})}),s.jsx(pl,{level:1,className:"text-purple-600 mb-4",children:"作品画廊"}),s.jsxs(xl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["作品画廊功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造精美的作品展示空间。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(q,{size:"large",icon:s.jsx(Ve,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:yl,Paragraph:bl}=Le,jl=()=>{const e=Ye();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Bt,{className:"text-4xl text-white"})}),s.jsx(yl,{level:1,className:"text-orange-600 mb-4",children:"排行榜"}),s.jsxs(bl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["排行榜功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造激励性的成就展示系统。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(q,{size:"large",icon:s.jsx(Ve,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})};class vl{constructor(){_t(this,"baseUrl","/traces")}async createSession(t){try{return(await Pe.post(`${this.baseUrl}/sessions`,t)).data}catch(n){throw console.error("创建轨迹会话失败:",n),n}}async getSessions(t=50,n=0){try{return(await Pe.get(`${this.baseUrl}/sessions`,{params:{limit:t,offset:n}})).data}catch(r){throw console.error("获取轨迹会话列表失败:",r),r}}async getSession(t){try{return(await Pe.get(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("获取轨迹会话失败:",n),n}}async deleteSession(t){try{return(await Pe.delete(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("删除轨迹会话失败:",n),n}}async generateGuidePaths(t,n={}){try{const r={session_id:t,simplification_level:n.simplification_level||"medium",min_stroke_length:n.min_stroke_length||20,merge_distance:n.merge_distance||50};return(await Pe.post(`${this.baseUrl}/sessions/${t}/generate-guide`,r)).data}catch(r){throw console.error("生成引导线失败:",r),r}}exportSessionAsJson(t){const n=JSON.stringify(t,null,2),r=new Blob([n],{type:"application/json"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download=`trace_${t.name}_${Date.now()}.json`,o.click(),URL.revokeObjectURL(a)}async importSessionFromJson(t){return new Promise((n,r)=>{const a=new FileReader;a.onload=o=>{var i;try{const c=(i=o.target)==null?void 0:i.result,l=JSON.parse(c);if(!this.validateTraceSession(l))throw new Error("无效的轨迹数据格式");n(l)}catch(c){r(new Error("解析JSON文件失败: "+c.message))}},a.onerror=()=>{r(new Error("读取文件失败"))},a.readAsText(t)})}validateTraceSession(t){if(!t||typeof t!="object")return!1;const n=["id","name","strokes","canvasSize","createdAt","duration"];for(const r of n)if(!(r in t))return!1;return!(!Array.isArray(t.strokes)||!t.canvasSize||typeof t.canvasSize!="object"||!("width"in t.canvasSize)||!("height"in t.canvasSize))}saveToLocalStorage(t){try{const n=this.getFromLocalStorage(),r=n.findIndex(a=>a.id===t.id);r>=0?n[r]=t:n.push(t),localStorage.setItem("traceSessions",JSON.stringify(n))}catch(n){throw console.error("保存到本地存储失败:",n),n}}getFromLocalStorage(){try{const t=localStorage.getItem("traceSessions");return t?JSON.parse(t):[]}catch(t){return console.error("从本地存储读取失败:",t),[]}}deleteFromLocalStorage(t){try{const r=this.getFromLocalStorage().filter(a=>a.id!==t);localStorage.setItem("traceSessions",JSON.stringify(r))}catch(n){throw console.error("从本地存储删除失败:",n),n}}clearLocalStorage(){try{localStorage.removeItem("traceSessions")}catch(t){throw console.error("清空本地存储失败:",t),t}}calculateSessionStats(t){const n=t.strokes.length;let r=0,a=0;return t.strokes.forEach(o=>{r+=o.points.length;for(let i=1;i<o.points.length;i++){const c=o.points[i].x-o.points[i-1].x,l=o.points[i].y-o.points[i-1].y;a+=Math.sqrt(c*c+l*l)}}),{totalStrokes:n,totalPoints:r,totalLength:a,averageStrokeLength:n>0?a/n:0,duration:t.duration}}}const Te=new vl,{Title:wl,Text:Fe}=Le,{Option:Dt}=Gt,Sl=()=>{const e=u.useRef(null),[t,n]=u.useState(!1),[r,a]=u.useState(!1),[o,i]=u.useState(!1),[c,l]=u.useState(null),[h,d]=u.useState([]),[m,v]=u.useState(""),[R,b]=u.useState(""),[j,x]=u.useState("#000000"),[T,C]=u.useState(3),[E,z]=u.useState({width:800,height:600}),[K,ie]=u.useState(null),[se,ge]=u.useState(null),[w,$]=u.useState(0),[S,k]=u.useState(null),[J,W]=u.useState(!1),[Q,de]=u.useState(!1),[X,pe]=u.useState(null),[y,L]=u.useState(null),H=u.useRef(null),oe=u.useCallback(g=>{h.forEach(M=>{if(!(M.points.length<2)){g.strokeStyle="#000000",g.lineWidth=3,g.lineCap="round",g.lineJoin="round",g.beginPath(),g.moveTo(M.points[0].x,M.points[0].y);for(let D=1;D<M.points.length;D++)g.lineTo(M.points[D].x,M.points[D].y);g.stroke()}})},[h]),Z=u.useCallback((g,M)=>{if(!(M.points.length<2)){g.strokeStyle=j,g.lineWidth=T,g.lineCap="round",g.lineJoin="round",g.beginPath(),g.moveTo(M.points[0].x,M.points[0].y);for(let D=1;D<M.points.length;D++)g.lineTo(M.points[D].x,M.points[D].y);g.stroke()}},[j,T]),V=u.useCallback(()=>{const g=e.current;if(!g)return;const M=g.getContext("2d");M&&(M.clearRect(0,0,g.width,g.height),H.current&&M.drawImage(H.current,0,0,g.width,g.height),oe(M),c&&c.points.length>0&&Z(M,c))},[h,oe,c,Z]);u.useEffect(()=>{if(y){const g=new Image;g.onload=()=>{H.current=g,V()},g.src=y}else H.current=null,V()},[y,V]),u.useEffect(()=>{V()},[c,V]),u.useEffect(()=>{const g=e.current;if(!g)return;const M=g.getContext("2d");if(!M)return;g.width=E.width,g.height=E.height,g.style.width=`${E.width}px`,g.style.height=`${E.height}px`,M.lineCap="round",M.lineJoin="round";const D=le=>{if(!t||r)return;le.preventDefault(),le.stopPropagation();const ye=le.touches[0],ke=g.getBoundingClientRect(),Ue=g.width/ke.width,Ge=g.height/ke.height,Oe={x:(ye.clientX-ke.left)*Ue,y:(ye.clientY-ke.top)*Ge};i(!0);const N={id:`stroke_${Date.now()}`,points:[Oe]};l(N)},te=le=>{if(!o||!c||!t||r)return;le.preventDefault(),le.stopPropagation();const ye=le.touches[0],ke=g.getBoundingClientRect(),Ue=g.width/ke.width,Ge=g.height/ke.height,Oe={x:(ye.clientX-ke.left)*Ue,y:(ye.clientY-ke.top)*Ge},f={...c,points:[...c.points,Oe]};l(f)},xe=le=>{le.preventDefault(),le.stopPropagation(),!(!o||!c||!t||r)&&(i(!1),d(ye=>[...ye,c]),l(null))};return g.addEventListener("touchstart",D,{passive:!1}),g.addEventListener("touchmove",te,{passive:!1}),g.addEventListener("touchend",xe,{passive:!1}),V(),()=>{g.removeEventListener("touchstart",D),g.removeEventListener("touchmove",te),g.removeEventListener("touchend",xe)}},[E,V,t,r,o,c]);const ue=u.useCallback(()=>{const g=e.current;if(!g)return;const M=g.getContext("2d");M&&(M.clearRect(0,0,g.width,g.height),H.current&&M.drawImage(H.current,0,0,g.width,g.height))},[]),ee=g=>{const M=e.current;if(!M)return{x:0,y:0};const D=M.getBoundingClientRect(),te=M.width/D.width,xe=M.height/D.height,le=(g.clientX-D.left)*te,ye=(g.clientY-D.top)*xe;return{x:le,y:ye}},Ae=g=>{if(!t||r)return;g.preventDefault(),i(!0);const M=ee(g),te={id:`stroke_${Date.now()}`,points:[M]};l(te)},$e=g=>{if(!o||!c||!t||r)return;g.preventDefault();const M=ee(g),D={...c,points:[...c.points,M]};l(D)},Xe=()=>{!o||!c||(i(!1),d(g=>[...g,c]),l(null))},Pt=()=>{if(!m.trim()){Y.error("请输入会话名称");return}n(!0),a(!1),ge(Date.now()),$(0),d([]),ue(),Y.success("开始录制轨迹")},Rt=()=>{t&&(r?(S&&$(g=>g+(Date.now()-S)),k(null),a(!1),Y.info("恢复录制")):(k(Date.now()),a(!0),i(!1),l(null),Y.info("暂停录制")))},Re=()=>{n(!1),a(!1),i(!1),l(null),S&&($(g=>g+(Date.now()-S)),k(null)),Y.success("录制已停止")},ot=()=>{d([]),ue(),Y.info("画布已清空")},je=(g,M=3)=>g.filter(D=>{if(D.points.length<2)return!1;let te=0;for(let xe=1;xe<D.points.length;xe++){const le=D.points[xe].x-D.points[xe-1].x,ye=D.points[xe].y-D.points[xe-1].y;te+=Math.sqrt(le*le+ye*ye)}return te>=M}).map(D=>{if(D.points.length<10)return D;const te=it(D.points),xe=Je(te);return{...D,points:xe}}),it=(g,M=2)=>{if(g.length<=3)return g;const D=[];D.push(g[0]);for(let te=1;te<g.length-1;te++){const xe=g[te-1],le=g[te],ye=g[te+1],ke=(xe.x+le.x*2+ye.x)/4,Ue=(xe.y+le.y*2+ye.y)/4;D.push({x:ke,y:Ue})}return D.push(g[g.length-1]),D},Je=(g,M=.5)=>{if(g.length<=3)return g;const D=[g[0]];for(let te=1;te<g.length-1;te++){const xe=D[D.length-1],le=g[te];Math.sqrt(Math.pow(le.x-xe.x,2)+Math.pow(le.y-xe.y,2))>M&&D.push(le)}return D.push(g[g.length-1]),D},ft=async()=>{if(h.length===0){Y.error("没有轨迹数据可保存");return}de(!0);try{const g=se?Date.now()-se:0,M=je(h);console.log(`轨迹优化完成: 原始笔画数 ${h.length}, 优化后笔画数 ${M.length}`);const D={name:m,description:R,strokes:M,canvasSize:E,duration:g-w};try{const te=await Te.createSession(D);if(te.success&&te.data)Y.success("轨迹数据已保存到服务器"),console.log("保存成功，会话ID:",te.data.id);else throw new Error(te.message)}catch(te){console.warn("API保存失败，使用本地存储:",te);const xe={id:`session_${Date.now()}`,name:m,description:R,strokes:h,canvasSize:E,createdAt:new Date().toISOString(),duration:g-w};Te.saveToLocalStorage(xe),Y.success("轨迹数据已保存到本地")}v(""),b(""),d([]),ue()}catch(g){console.error("保存失败:",g),Y.error("保存失败，请重试")}finally{de(!1)}},ms=g=>{if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(g.type))return Y.error("请上传图片文件 (JPG, PNG, GIF, WebP)"),!1;const D=10*1024*1024;if(g.size>D)return Y.error("图片文件大小不能超过 10MB"),!1;const te=new FileReader;return te.onload=xe=>{var ke;const le=(ke=xe.target)==null?void 0:ke.result;L(le);const ye=new Image;ye.onload=()=>{const Ue=Math.min(window.innerWidth*.8,1200),Ge=Math.min(window.innerHeight*.7,800);let Oe=ye.width,f=ye.height;Oe>Ue&&(f=f*Ue/Oe,Oe=Ue),f>Ge&&(Oe=Oe*Ge/f,f=Ge);const N={width:Math.round(Oe),height:Math.round(f)};z(N),Y.success(`背景图已加载，画布尺寸调整为 ${Math.round(Oe)} × ${Math.round(f)}`)},ye.src=le},te.readAsDataURL(g),!1},qe=()=>{L(null),z({width:800,height:600}),Y.info("背景图已移除")},fs=()=>{d([]),qe(),ue(),Y.info("画布和背景图已清空")},he=()=>{if(h.length===0){Y.error("没有轨迹数据可导出");return}const g=se?Date.now()-se:0,M=je(h);console.log(`导出轨迹优化完成: 原始笔画数 ${h.length}, 优化后笔画数 ${M.length}`);const D={id:`session_${Date.now()}`,name:m||"未命名会话",description:R,strokes:M,canvasSize:E,createdAt:new Date().toISOString(),duration:g-w};Te.exportSessionAsJson(D),Y.success("轨迹数据已导出")};return s.jsxs("div",{className:"trace-recorder min-h-screen p-3 bg-gray-50",children:[s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx(re.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-4",children:[s.jsx("div",{className:"xl:col-span-1",children:s.jsx(ce,{size:"small",className:"mb-3",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx(Fe,{strong:!0,className:"block mb-2",children:"会话信息"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(Hs,{placeholder:"会话名称",value:m,onChange:g=>v(g.target.value),disabled:t,size:"small"}),s.jsx(Hs.TextArea,{placeholder:"会话描述（可选）",value:R,onChange:g=>b(g.target.value),disabled:t,rows:2,size:"small"})]})]}),s.jsxs("div",{children:[s.jsx(Fe,{strong:!0,className:"block mb-2",children:"背景图"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(q,{icon:s.jsx(ct,{}),onClick:()=>{const g=document.createElement("input");g.type="file",g.accept="image/*",g.onchange=M=>{var te;const D=(te=M.target.files)==null?void 0:te[0];D&&ms(D)},g.click()},disabled:t,className:"w-full",size:"small",children:"上传背景图"}),y&&s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Fe,{type:"success",style:{fontSize:"11px"},children:"✓ 已加载"}),s.jsx(q,{icon:s.jsx(ws,{}),size:"small",danger:!0,onClick:qe,disabled:t,children:"移除"})]})]})]}),s.jsxs("div",{children:[s.jsx(Fe,{strong:!0,className:"block mb-2",children:"画笔设置"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Fe,{style:{fontSize:"12px",width:"30px"},children:"颜色"}),s.jsx("input",{type:"color",value:j,onChange:g=>x(g.target.value),disabled:t&&!r,className:"w-8 h-6 border rounded"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Fe,{style:{fontSize:"12px",width:"30px"},children:"大小"}),s.jsxs(Gt,{value:T,onChange:C,disabled:t&&!r,className:"flex-1",size:"small",children:[s.jsx(Dt,{value:1,children:"细 (1px)"}),s.jsx(Dt,{value:3,children:"中 (3px)"}),s.jsx(Dt,{value:5,children:"粗 (5px)"}),s.jsx(Dt,{value:8,children:"很粗 (8px)"})]})]})]})]}),s.jsxs("div",{children:[s.jsx(wl,{level:4,children:"录制控制"}),s.jsxs(fe,{wrap:!0,children:[t?s.jsxs(s.Fragment,{children:[s.jsx(q,{icon:r?s.jsx(We,{}):s.jsx(Rn,{}),onClick:Rt,size:"large",children:r?"恢复":"暂停"}),s.jsx(q,{icon:s.jsx(Mr,{}),onClick:Re,size:"large",danger:!0,children:"停止"})]}):s.jsx(q,{type:"primary",icon:s.jsx(We,{}),onClick:Pt,size:"large",children:"开始录制"}),s.jsx(q,{icon:s.jsx(Tn,{}),onClick:ot,disabled:t&&!r,children:"清空轨迹"}),y&&s.jsx(q,{icon:s.jsx(ws,{}),onClick:fs,disabled:t&&!r,danger:!0,children:"清空全部"})]})]}),s.jsxs("div",{children:[s.jsx(Fe,{strong:!0,className:"block mb-2",children:"保存操作"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(q,{type:"primary",icon:s.jsx(Pn,{}),onClick:ft,disabled:h.length===0||Q,loading:Q,className:"w-full",size:"small",children:Q?"保存中...":"保存轨迹"}),s.jsx(q,{icon:s.jsx($s,{}),onClick:he,disabled:h.length===0,className:"w-full",size:"small",children:"导出JSON"}),s.jsx(q,{icon:s.jsx(_n,{}),onClick:()=>W(!0),disabled:h.length===0,className:"w-full",size:"small",children:"预览轨迹"})]})]}),t&&s.jsxs("div",{className:"mt-4 p-2 bg-gray-100 rounded",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:`w-2 h-2 rounded-full ${r?"bg-yellow-500":"bg-red-500 animate-pulse"}`}),s.jsx(Fe,{style:{fontSize:"11px"},strong:!0,children:r?"录制已暂停":"正在录制..."})]}),s.jsxs(Fe,{style:{fontSize:"11px"},type:"secondary",children:["笔画数: ",h.length]})]})]})})}),s.jsx("div",{className:"xl:col-span-3",children:s.jsx(ce,{size:"small",children:s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("div",{className:"mb-2 text-center",children:s.jsxs(Fe,{type:"secondary",style:{fontSize:"12px"},children:["画布: ",E.width," × ",E.height,y&&" • 已加载背景图"]})}),s.jsx("div",{className:"relative",children:s.jsx("canvas",{ref:e,width:E.width,height:E.height,className:`border-2 border-gray-300 rounded-lg cursor-crosshair shadow-md ${y?"bg-transparent":"bg-white"}`,style:{touchAction:"none",userSelect:"none",maxWidth:"100%",maxHeight:"70vh",position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:Ae,onMouseMove:$e,onMouseUp:Xe,onMouseLeave:()=>i(!1)})})]})})})]})})}),s.jsx(Ht,{title:"轨迹预览",open:J,onCancel:()=>W(!1),footer:null,width:900,children:s.jsxs("div",{className:"text-center",children:[s.jsxs(Fe,{children:["会话: ",m||"未命名"]}),s.jsx("br",{}),s.jsxs(Fe,{type:"secondary",children:["笔画数: ",h.length]}),s.jsx("br",{}),s.jsxs(Fe,{type:"secondary",children:["录制时长: ",Math.round((Date.now()-(se||Date.now())-w)/1e3),"秒"]})]})})]})},{Title:Nl,Text:Me}=Le,{Option:vs}=Gt,El=({onGenerateGuide:e})=>{const[t,n]=u.useState([]),[r,a]=u.useState(!1),[o,i]=u.useState(null),[c,l]=u.useState(!1),[h,d]=u.useState(!1),[m,v]=u.useState(null),[R,b]=u.useState({simplification_level:"medium",min_stroke_length:20,merge_distance:50}),[j,x]=u.useState(!1),T=async()=>{a(!0);try{let S=[];try{const k=await Te.getSessions();S=[...k],console.log("从API加载了",k.length,"个会话")}catch(k){console.warn("API加载失败:",k)}try{const k=Te.getFromLocalStorage();console.log("从本地存储加载了",k.length,"个会话");const J=new Set(S.map(Q=>Q.id)),W=k.filter(Q=>!J.has(Q.id));S=[...S,...W]}catch(k){console.warn("本地存储加载失败:",k)}S.sort((k,J)=>new Date(J.createdAt).getTime()-new Date(k.createdAt).getTime()),n(S),console.log("总共加载了",S.length,"个会话")}catch(S){console.error("加载轨迹会话失败:",S),Y.error("加载轨迹会话失败")}finally{a(!1)}};u.useEffect(()=>{T()},[]);const C=async S=>{try{try{await Te.deleteSession(S)}catch(k){console.warn("API删除失败，从本地存储删除:",k),Te.deleteFromLocalStorage(S)}Y.success("会话删除成功"),T()}catch(k){console.error("删除会话失败:",k),Y.error("删除会话失败")}},E=S=>{i(S),l(!0)},z=S=>{Te.exportSessionAsJson(S),Y.success("会话数据已导出")},K=()=>{const S=document.createElement("input");S.type="file",S.accept=".json",S.onchange=async k=>{var W;const J=(W=k.target.files)==null?void 0:W[0];if(J)try{const Q=await Te.importSessionFromJson(J);try{await Te.createSession({name:Q.name,description:Q.description,strokes:Q.strokes,canvasSize:Q.canvasSize,duration:Q.duration})}catch(de){console.warn("API保存失败，保存到本地存储:",de),Te.saveToLocalStorage(Q)}Y.success("会话导入成功"),T()}catch(Q){console.error("导入会话失败:",Q),Y.error("导入会话失败: "+Q.message)}},S.click()},ie=S=>{v(S),d(!0)},se=async()=>{if(m){x(!0);try{let S=m,k=!1;try{await Te.getSession(m.id)}catch{console.log("会话不存在于API中，尝试上传..."),k=!0}if(k)try{const W=await Te.createSession({name:m.name,description:m.description,strokes:m.strokes,canvasSize:m.canvasSize,duration:m.duration});if(W.success&&W.data)S=W.data,console.log("会话上传成功，新ID:",S.id);else throw new Error("上传会话失败")}catch(W){console.error("上传会话失败:",W),Y.error("无法上传会话到服务器，请检查网络连接");return}const J=await Te.generateGuidePaths(S.id,R);if(J.success&&J.guide_paths)Y.success("引导线生成成功"),d(!1),e&&J.canvas_size&&e(J.guide_paths,J.canvas_size,void 0),k&&T();else throw new Error(J.message||"生成引导线失败")}catch(S){console.error("生成引导线失败:",S),Y.error("生成引导线失败: "+S.message)}finally{x(!1)}}},ge=S=>{const k=Math.floor(S/1e3),J=Math.floor(k/60),W=k%60;return`${J}:${W.toString().padStart(2,"0")}`},w=S=>Te.calculateSessionStats(S),$=[{title:"会话名称",dataIndex:"name",key:"name",render:(S,k)=>s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:S}),k.description&&s.jsx("div",{children:s.jsx(Me,{type:"secondary",style:{fontSize:"12px"},children:k.description})})]})},{title:"统计信息",key:"stats",render:(S,k)=>{const J=w(k);return s.jsxs(fe,{direction:"vertical",size:"small",children:[s.jsxs(Qe,{color:"blue",children:[J.totalStrokes," 笔画"]}),s.jsxs(Qe,{color:"green",children:[J.totalPoints," 点"]}),s.jsx(Qe,{color:"orange",children:ge(J.duration)})]})}},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:S=>new Date(S).toLocaleString()},{title:"操作",key:"actions",render:(S,k)=>s.jsxs(fe,{children:[s.jsx(It,{title:"预览",children:s.jsx(q,{icon:s.jsx(_n,{}),onClick:()=>E(k),size:"small"})}),s.jsx(It,{title:"生成引导线",children:s.jsx(q,{icon:s.jsx(We,{}),onClick:()=>ie(k),size:"small",type:"primary"})}),s.jsx(It,{title:"导出",children:s.jsx(q,{icon:s.jsx($s,{}),onClick:()=>z(k),size:"small"})}),s.jsx(It,{title:"删除",children:s.jsx(kn,{title:"确定要删除这个会话吗？",onConfirm:()=>C(k.id),okText:"确定",cancelText:"取消",children:s.jsx(q,{icon:s.jsx(ws,{}),danger:!0,size:"small"})})})]})}];return s.jsxs("div",{className:"trace-manager",children:[s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(Nl,{level:3,children:"轨迹管理"}),s.jsxs(fe,{children:[s.jsx(q,{icon:s.jsx(dt,{}),onClick:T,loading:r,children:"刷新"}),s.jsx(q,{icon:s.jsx(Ur,{}),onClick:K,children:"导入会话"})]})]}),s.jsx(zr,{columns:$,dataSource:t,rowKey:"id",loading:r,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:S=>`共 ${S} 个会话`}})]})}),s.jsx(Ht,{title:`预览会话: ${o==null?void 0:o.name}`,open:c,onCancel:()=>l(!1),footer:null,width:800,children:o&&s.jsx("div",{children:s.jsxs(fe,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:"描述: "}),s.jsx(Me,{children:o.description||"无"})]}),s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:"画布尺寸: "}),s.jsxs(Me,{children:[o.canvasSize.width," × ",o.canvasSize.height]})]}),s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:"背景图: "}),s.jsx(Me,{children:"无"})]}),s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:"统计信息: "}),(()=>{const S=w(o);return s.jsxs("div",{children:[s.jsxs(Qe,{color:"blue",children:[S.totalStrokes," 笔画"]}),s.jsxs(Qe,{color:"green",children:[S.totalPoints," 点"]}),s.jsx(Qe,{color:"orange",children:ge(S.duration)}),s.jsxs(Qe,{color:"purple",children:[Math.round(S.totalLength)," 像素长度"]})]})})()]})]})})}),s.jsx(Ht,{title:`生成引导线: ${m==null?void 0:m.name}`,open:h,onCancel:()=>d(!1),onOk:se,confirmLoading:j,okText:"生成",cancelText:"取消",children:s.jsxs(fe,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:"简化级别:"}),s.jsxs(Gt,{value:R.simplification_level,onChange:S=>b(k=>({...k,simplification_level:S})),className:"w-full mt-2",children:[s.jsx(vs,{value:"low",children:"低 (保留更多细节)"}),s.jsx(vs,{value:"medium",children:"中 (平衡)"}),s.jsx(vs,{value:"high",children:"高 (更简化)"})]})]}),s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:"最小笔画长度 (像素):"}),s.jsx(Ws,{value:R.min_stroke_length,onChange:S=>b(k=>({...k,min_stroke_length:S||20})),min:1,max:200,className:"w-full mt-2"})]}),s.jsxs("div",{children:[s.jsx(Me,{strong:!0,children:"合并距离 (像素):"}),s.jsx(Ws,{value:R.merge_distance,onChange:S=>b(k=>({...k,merge_distance:S||50})),min:1,max:200,className:"w-full mt-2"})]}),s.jsx(Br,{}),s.jsx(Me,{type:"secondary",children:"这些设置将影响生成的引导线的复杂度和精度。较高的简化级别会产生更简单的引导线， 较大的合并距离会将相近的笔画合并为一条路径。"})]})})]})},{Title:kl,Text:Ke}=Le,Cl=({guidePaths:e,canvasSize:t,onExport:n})=>{const r=u.useRef(null),[a,o]=u.useState(!1),[i,c]=u.useState(0),[l,h]=u.useState(1),[d,m]=u.useState(!0),[v,R]=u.useState(0),b=u.useRef(null),j=()=>{const w=r.current;if(!w)return;const $=w.getContext("2d");$&&$.clearRect(0,0,w.width,w.height)},x=()=>{const w=r.current;if(!w)return;const $=w.getContext("2d");$&&(j(),d?e.forEach((S,k)=>{T($,S,k<=v)}):v<e.length&&T($,e[v],!0))},T=(w,$,S)=>{if(!($.points.length<2)){w.strokeStyle=S?"#FFD700":"#E5E7EB",w.lineWidth=S?3:2,w.lineCap="round",w.lineJoin="round",S?w.setLineDash([10,5]):w.setLineDash([5,5]),w.beginPath(),w.moveTo($.points[0].x,$.points[0].y);for(let k=1;k<$.points.length;k++)w.lineTo($.points[k].x,$.points[k].y);w.stroke(),w.setLineDash([])}},C=()=>{const w=r.current;if(!w)return;const $=w.getContext("2d");$&&(j(),d?e.forEach((S,k)=>{k<v?T($,S,!0):k===v?E($,S,i):T($,S,!1)}):v<e.length&&E($,e[v],i))},E=(w,$,S)=>{if($.points.length<2)return;const k=$.points.length,J=Math.floor(k*S);if(w.strokeStyle="#FFD700",w.lineWidth=3,w.lineCap="round",w.lineJoin="round",w.setLineDash([10,5]),w.beginPath(),J>0){w.moveTo($.points[0].x,$.points[0].y);for(let W=1;W<J;W++)w.lineTo($.points[W].x,$.points[W].y);if(J<k){const W=$.points[J-1],Q=$.points[J],de=k*S-J,X=W.x+(Q.x-W.x)*de,pe=W.y+(Q.y-W.y)*de;w.lineTo(X,pe)}}w.stroke(),w.setLineDash([]),S>.1&&J>0&&z(w,$,S)},z=(w,$,S)=>{const k=$.points.length,J=Math.floor(k*S);let W,Q,de,X;if(J<k){const H=$.points[J-1],oe=$.points[J],Z=k*S-J;W=H.x+(oe.x-H.x)*Z,Q=H.y+(oe.y-H.y)*Z,de=oe.x-H.x,X=oe.y-H.y}else{W=$.points[k-1].x,Q=$.points[k-1].y,de=0,X=0;const H=Math.min(5,k-1);for(let oe=1;oe<=H;oe++){const Z=$.points[k-oe],V=$.points[k-oe-1];de+=Z.x-V.x,X+=Z.y-V.y}de/=H,X/=H}const pe=Math.sqrt(de*de+X*X);if(pe<.1)return;de/=pe,X/=pe;const y=Math.atan2(X,de),L=15;w.fillStyle="#FFD700",w.strokeStyle="#FFD700",w.lineWidth=2,w.beginPath(),w.moveTo(W,Q),w.lineTo(W-L*Math.cos(y-Math.PI/6),Q-L*Math.sin(y-Math.PI/6)),w.lineTo(W-L*Math.cos(y+Math.PI/6),Q-L*Math.sin(y+Math.PI/6)),w.closePath(),w.fill(),w.stroke()},K=()=>{a&&(c(w=>{const $=w+.01*l;return $>=1?d&&v<e.length-1?(R(S=>S+1),0):(o(!1),1):$}),b.current=requestAnimationFrame(K))},ie=()=>{a?(o(!1),b.current&&cancelAnimationFrame(b.current)):o(!0)},se=()=>{o(!1),c(0),R(0),b.current&&cancelAnimationFrame(b.current)},ge=()=>{if(n)n(e);else{const w=JSON.stringify({guidePaths:e,canvasSize:t,exportedAt:new Date().toISOString()},null,2),$=new Blob([w],{type:"application/json"}),S=URL.createObjectURL($),k=document.createElement("a");k.href=S,k.download=`guide_paths_${Date.now()}.json`,k.click(),URL.revokeObjectURL(S),Y.success("引导线数据已导出")}};return u.useEffect(()=>(a&&(b.current=requestAnimationFrame(K)),()=>{b.current&&cancelAnimationFrame(b.current)}),[a,l,v,d]),u.useEffect(()=>{a?C():x()},[e,i,v,d,a]),e.length===0?s.jsx(ce,{children:s.jsx("div",{className:"text-center py-8",children:s.jsx(Ke,{type:"secondary",children:"暂无引导线数据"})})}):s.jsx("div",{className:"guide-preview",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx(kl,{level:4,children:"引导线预览"}),s.jsxs(fe,{children:[s.jsx(q,{icon:a?s.jsx(Rn,{}):s.jsx(We,{}),onClick:ie,type:"primary",children:a?"暂停":"播放"}),s.jsx(q,{icon:s.jsx(dt,{}),onClick:se,children:"重置"}),s.jsx(q,{icon:s.jsx($s,{}),onClick:ge,children:"导出"})]})]}),s.jsx("div",{className:"mb-4 p-4 bg-gray-50 rounded-lg",children:s.jsxs(fe,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Ke,{children:"动画速度:"}),s.jsx("div",{className:"w-48",children:s.jsx(Cn,{min:.1,max:3,step:.1,value:l,onChange:h,disabled:a})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Ke,{children:"显示所有路径:"}),s.jsx(qr,{checked:d,onChange:m,disabled:a})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Ke,{children:"路径信息:"}),s.jsxs(Ke,{type:"secondary",children:["共 ",e.length," 条路径，当前第 ",v+1," 条"]})]})]})}),s.jsx("div",{className:"flex justify-center",children:s.jsx("canvas",{ref:r,width:t.width,height:t.height,className:"border-2 border-gray-300 rounded-lg bg-white",style:{maxWidth:"100%",maxHeight:"600px"}})}),s.jsx("div",{className:"mt-4 text-center",children:s.jsxs(fe,{children:[s.jsxs(Ke,{type:"secondary",children:["画布尺寸: ",t.width," × ",t.height]}),s.jsxs(Ke,{type:"secondary",children:["路径数量: ",e.length]}),s.jsxs(Ke,{type:"secondary",children:["总点数: ",e.reduce((w,$)=>w+$.points.length,0)]})]})})]})})})},Tl=()=>{const[e,t]=u.useState("recorder"),[n,r]=u.useState([]),[a,o]=u.useState({width:800,height:600}),[i,c]=u.useState(),l=(h,d,m)=>{r(h),o(d),c(m),t("preview")};return s.jsx("div",{className:"trace-recorder-page min-h-screen",style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:s.jsx("div",{className:"container mx-auto px-4 py-6",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsx(ce,{className:"shadow-lg",children:s.jsx(Hr,{activeKey:e,onChange:t,size:"large",className:"trace-tabs",items:[{key:"recorder",label:"📝 轨迹记录",children:s.jsx(Sl,{})},{key:"manager",label:"📁 轨迹管理",children:s.jsx(El,{onGenerateGuide:l})},{key:"preview",label:`🎯 引导线预览 ${n.length>0?`(${n.length})`:""}`,children:s.jsx(Cl,{guidePaths:n,canvasSize:a,backgroundImage:i})}]})})})})})},{Title:wn,Text:Sn}=Le,Pl=()=>{const e=Ye(),t=[{id:"L1-1",name:"第一关第一阶段",type:"L1"},{id:"L1-2",name:"第一关第二阶段",type:"L1"},{id:"L1-3",name:"第一关第三阶段",type:"L1"},{id:"L1-4",name:"第一关第四阶段",type:"L1"},{id:"L1-5",name:"第一关第五阶段",type:"L1"},{id:"L2-1",name:"第二关第一阶段 (统一背景)",type:"other"},{id:"L3-1",name:"第三关第一阶段 (统一背景)",type:"other"},{id:"L4-1",name:"第四关第一阶段 (统一背景)",type:"other"}],n=r=>{const[a,o]=r.split("-");e(`/game/${a}/${o}`)};return s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx(wn,{level:1,className:"text-white",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8)"},children:"背景图片测试"}),s.jsx(Sn,{className:"text-white text-lg",style:{textShadow:"1px 1px 2px rgba(0,0,0,0.8)"},children:"点击下面的按钮测试不同关卡的背景图片切换"})]}),s.jsx("div",{className:"text-center",children:s.jsxs(fe,{direction:"vertical",size:"large",children:[s.jsx(fe,{wrap:!0,size:"middle",children:t.map(r=>s.jsx(q,{type:r.type==="L1"?"primary":"default",size:"large",onClick:()=>n(r.id),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",borderColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",color:"white",backdropFilter:"blur(10px)"},children:r.name},r.id))}),s.jsx(q,{type:"default",size:"large",onClick:()=>e("/"),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)"},children:"返回首页"})]})}),s.jsx("div",{className:"mt-12 text-center",children:s.jsxs("div",{className:"inline-block p-6 rounded-lg",style:{backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)",maxWidth:"600px"},children:[s.jsx(wn,{level:3,children:"测试说明"}),s.jsxs(Sn,{children:[s.jsx("strong",{children:"紫色按钮 (L1关卡)"}),"：使用各自目录下的background.jpg文件作为背景图片。",s.jsx("br",{}),"例如：L1-2关卡显示 /L1-2/background.jpg 作为背景。",s.jsx("br",{}),s.jsx("strong",{children:"绿色按钮 (其他关卡)"}),"：统一使用 /background.jpg 作为背景图片。",s.jsx("br",{}),s.jsx("strong",{children:"其他页面"}),"：轨迹记录、首页等非游戏页面也使用统一背景 /background.jpg。"]})]})})]})})};class Rl extends u.Component{constructor(n){super(n);_t(this,"handleReload",()=>{window.location.reload()});_t(this,"handleGoHome",()=>{window.location.href="/"});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsx("div",{className:"max-w-2xl mx-auto p-8",children:s.jsx(Wr,{status:"error",title:"哎呀，出现了一些问题",subTitle:"不用担心，这不是您的错误。我们的技术团队会尽快解决这个问题。",extra:[s.jsx(q,{type:"primary",icon:s.jsx(dt,{}),onClick:this.handleReload,size:"large",children:"重新加载"},"reload"),s.jsx(q,{icon:s.jsx(In,{}),onClick:this.handleGoHome,size:"large",children:"返回首页"},"home")],children:s.jsxs("div",{className:"text-center mt-8",children:[s.jsx("p",{className:"text-gray-600 mb-4",children:"如果问题持续存在，请联系我们的客服团队："}),s.jsxs("p",{className:"text-gray-600",children:["📧 <EMAIL>",s.jsx("br",{}),"📞 400-123-4567"]}),s.jsx("div",{className:"mt-6 p-4 bg-orange-50 rounded-lg",children:s.jsx("p",{className:"text-orange-600 font-medium",children:"💝 记忆画笔团队始终为您提供温暖的技术支持"})})]})})})}):this.props.children}}const _l=({visible:e,onClose:t,onEnterFullscreen:n})=>{const r=()=>{n(),t()};return s.jsx(Ht,{open:e,onCancel:t,footer:null,centered:!0,width:500,closable:!1,maskClosable:!1,className:"fullscreen-prompt-modal",children:s.jsxs("div",{className:"text-center p-6",children:[s.jsxs("div",{className:"mb-6",children:[s.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(Ss,{className:"text-3xl text-white"})}),s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-3",children:"获得最佳体验"}),s.jsxs("p",{className:"text-lg text-gray-600 leading-relaxed",children:["为了获得最佳的绘画体验，建议您使用全屏模式。",s.jsx("br",{}),"全屏模式可以让您专注于创作，减少干扰。"]})]}),s.jsxs(fe,{size:"large",className:"w-full justify-center",children:[s.jsx(q,{type:"primary",size:"large",icon:s.jsx(Ss,{}),onClick:r,className:"h-12 px-8 text-lg bg-gradient-to-r from-purple-500 to-purple-600 border-0",children:"进入全屏"}),s.jsx(q,{size:"large",icon:s.jsx(Ln,{}),onClick:t,className:"h-12 px-8 text-lg",children:"稍后再说"})]}),s.jsx("div",{className:"mt-6 text-sm text-gray-500",children:s.jsx("p",{children:"您也可以随时通过右上角的全屏按钮切换全屏模式"})})]})})},Il=()=>{const e=Ye(),t=Qt(),[n,r]=u.useState(!1),[a,o]=u.useState(!!document.fullscreenElement);Ze.useEffect(()=>{const l=()=>{o(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",l),()=>{document.removeEventListener("fullscreenchange",l)}},[]);const i=async()=>{try{document.fullscreenElement?await document.exitFullscreen():await document.documentElement.requestFullscreen()}catch(l){console.error("全屏切换失败:",l)}},c=[{key:"home",icon:s.jsx(In,{}),label:"首页",onClick:()=>{e("/"),r(!1)}},{key:"game",icon:s.jsx(We,{}),label:"开始绘画",onClick:()=>{e("/game"),r(!1)}},{key:"gallery",icon:s.jsx(ct,{}),label:"作品画廊",onClick:()=>{e("/gallery"),r(!1)}},{key:"leaderboard",icon:s.jsx(Bt,{}),label:"排行榜",onClick:()=>{e("/leaderboard"),r(!1)}},{key:"trace-recorder",icon:s.jsx(Gr,{}),label:"轨迹记录",onClick:()=>{e("/trace-recorder"),r(!1)}},{key:"settings",icon:s.jsx(As,{}),label:"设置",onClick:()=>{e("/settings"),r(!1)}},{type:"divider"},{key:"fullscreen",icon:a?s.jsx(Vr,{}):s.jsx(Ss,{}),label:a?"退出全屏":"进入全屏",onClick:()=>{i(),r(!1)}}];return s.jsxs("div",{className:"fixed top-6 right-6 z-50",children:[s.jsx(Xr,{children:n&&s.jsx(re.div,{initial:{opacity:0,scale:.8,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:20},transition:{duration:.2},className:"mb-4",children:s.jsx("div",{className:"bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden",children:s.jsx("div",{className:"p-2",children:c.map((l,h)=>{if(l.type==="divider")return s.jsx("div",{className:"h-px bg-gray-200 my-2"},h);const d=l.key==="home"&&t.pathname==="/"||l.key==="game"&&t.pathname.startsWith("/game")||l.key==="gallery"&&t.pathname==="/gallery"||l.key==="leaderboard"&&t.pathname==="/leaderboard"||l.key==="trace-recorder"&&t.pathname==="/trace-recorder"||l.key==="settings"&&t.pathname==="/settings";return s.jsx(q,{type:d?"primary":"text",icon:l.icon,onClick:l.onClick,className:"w-full justify-start mb-1 h-12 text-left",size:"large",children:l.label},l.key)})})})})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(q,{type:"primary",shape:"circle",size:"large",icon:n?s.jsx(Ln,{}):s.jsx(Jr,{}),onClick:()=>r(!n),className:"w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 border-0 shadow-2xl hover:shadow-3xl",style:{fontSize:"18px"}})})]})},{Content:Ll}=Ft,{Title:Al,Paragraph:$l}=Le,Ol=()=>{const[e,t]=Ze.useState(!1),n=Ye();jo(),Ze.useEffect(()=>{if(!localStorage.getItem("memorybrush-fullscreen-prompt-seen")&&!document.fullscreenElement){const i=setTimeout(()=>{t(!0)},2e3);return()=>clearTimeout(i)}},[]);const r=async()=>{try{await document.documentElement.requestFullscreen(),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")}catch(o){console.error("进入全屏失败:",o)}},a=()=>{t(!1),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")};return s.jsxs(Rl,{children:[s.jsx(Ft,{className:"min-h-screen",style:{background:"transparent"},children:s.jsx(Ft,{children:s.jsx(Ft,{className:"transition-all duration-300",children:s.jsx(Ll,{className:"min-h-screen",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs(Ya,{children:[s.jsx(ze,{path:"/",element:s.jsx(vo,{})}),s.jsx(ze,{path:"/game",element:s.jsx(js,{})}),s.jsx(ze,{path:"/game/:level",element:s.jsx(js,{})}),s.jsx(ze,{path:"/game/:level/:stage",element:s.jsx(js,{})}),s.jsx(ze,{path:"/profile",element:s.jsx(ul,{})}),s.jsx(ze,{path:"/settings",element:s.jsx(fl,{})}),s.jsx(ze,{path:"/gallery",element:s.jsx(gl,{})}),s.jsx(ze,{path:"/leaderboard",element:s.jsx(jl,{})}),s.jsx(ze,{path:"/trace-recorder",element:s.jsx(Tl,{})}),s.jsx(ze,{path:"/background-test",element:s.jsx(Pl,{})}),s.jsx(ze,{path:"*",element:s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx("span",{className:"text-4xl text-white",children:"🔍"})}),s.jsx(Al,{level:1,className:"text-gray-600 mb-4",children:"页面建设中"}),s.jsxs($l,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["您访问的页面正在建设中，敬请期待！",s.jsx("br",{}),"我们正在努力为您提供更好的功能体验。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(q,{size:"large",icon:s.jsx(Ve,{}),onClick:()=>n("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})})]})})})})})}),s.jsx(Il,{}),s.jsx(_l,{visible:e,onClose:a,onEnterFullscreen:r})]})},Dl={token:{colorPrimary:"#8b5cf6",colorSuccess:"#10b981",colorWarning:"#f59e0b",colorError:"#ef4444",borderRadius:8,fontSize:16,fontFamily:"Inter, system-ui, sans-serif"},components:{Button:{fontSize:18,paddingContentHorizontal:24,paddingContentVertical:12},Input:{fontSize:16,paddingBlock:12},Card:{borderRadius:12}}};Ns.createRoot(document.getElementById("root")).render(s.jsx(Ze.StrictMode,{children:s.jsx(Qa,{children:s.jsx(Yr,{locale:bo,theme:Dl,componentSize:"large",children:s.jsx(Ol,{})})})}));
//# sourceMappingURL=index-DCwzp88B.js.map
